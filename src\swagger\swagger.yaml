openapi: 3.0.0
info:
  title: Boilerplate API Documentation
  version: 1.0.0
  description: Node typescript boilerplate api documentation
servers:
  - url: http://localhost:3000
    description: development
  - url: http://localhost:3000/
    description: localhost
components:
  schemas:
    User:
      type: object
      properties:
        id:
          type: integer
        username:
          type: string
        email:
          type: string
        createdAt:
          type: string
        updatedAt:
          type: string
paths:
  /:
    get:
      tags:
        - Common
      summary: Health check
      responses:
        '200':
          description: Health check of the service.
          content:
            application/json:
              schema:
                type: object
                properties:
                  uptime:
                    type: number
                  message:
                    type: string
                  date:
                    type: string
  /users:
    get:
      tags:
        - Users
      summary: Fetch users list
      parameters:
        - name: limit
          in: query
          schema:
            type: number
            default: 10
        - name: page
          in: query
          schema:
            type: number
            default: 1
        - name: sortOrder
          in: query
          schema:
            type: string
            default: ASC
        - name: sortField
          in: query
          schema:
            type: string
            default: id
        - name: searchKey
          in: query
          description: This value overrides username & email.
          schema:
            type: string
        - name: id
          in: query
          schema:
            type: integer
            format: int64
        - name: username
          in: query
          schema:
            type: string
        - name: email
          in: query
          schema:
            type: string
            format: email
        - name: createdAt
          in: query
          schema:
            type: Date
            format: Date
        - name: updatedAt
          in: query
          schema:
            type: string
            format: Date
      responses:
        '200':
          description: Users list.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User'
    post:
      tags:
        - Users
      summary: Create new user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                username:
                  type: string
                  minLength: 3
                email:
                  type: string
                  format: email
                password:
                  type: string
                  minLength: 6
              required:
                - username
                - email
                - password
      responses:
        '200':
          description: Create User.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
  /users/{id}:
    put:
      tags:
        - Users
      summary: Update a user
      parameters:
        - name: id
          in: path
          description: User ID
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                username:
                  type: string
                  minLength: 3
                email:
                  type: string
                  format: email
                password:
                  type: string
                  minLength: 6
              required:
                - username
                - email
                - password
      responses:
        '200':
          description: Create User.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User'
