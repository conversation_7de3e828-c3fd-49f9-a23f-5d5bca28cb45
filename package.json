{"name": "node-typescript-boilerplate", "version": "1.0.0", "description": "Provide a scalable and secure boilerplate that accelerates Node.js project setup while enforcing best practices and maintainable architecture from the start.", "main": "src/main.ts", "scripts": {"build": "nest build", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,test}/**/*.ts\"", "lint:fix": "eslint \"{src,test}/**/*.ts\" --fix", "prettier": "prettier . --check", "prettier:fix": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "test": "jest --config jest.config.ts", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:e2e": "jest --config ./test/jest-e2e.json", "prepare": "husky install", "commit": "git-cz", "typeorm": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js -d src/plugins/migration.plugin.ts", "migration:run": "npm run typeorm -- migration:run", "migration:revert": "npm run typeorm -- migration:revert", "commands": "node -r ts-node/register/transpile-only -r tsconfig-paths/register src/index.command.ts", "docker:prod": "docker-compose -f docker-compose.yml -f docker-compose.prod.yml up", "docker:dev": "docker-compose -f docker-compose.yml -f docker-compose.dev.yml up"}, "keywords": ["NodeJS", "Typescript", "NestJS"], "author": "<PERSON><PERSON>", "license": "ISC", "dependencies": {"@nestjs/common": "^11.1.5", "@nestjs/core": "^11.1.5", "@nestjs/platform-express": "^11.1.5", "@nestjs/typeorm": "^11.0.0", "@opentelemetry/api": "^1.9.0", "@opentelemetry/auto-instrumentations-node": "^0.62.0", "@opentelemetry/sdk-metrics": "^2.0.1", "@opentelemetry/sdk-node": "^0.203.0", "@opentelemetry/sdk-trace-node": "^2.0.1", "@types/joi": "^17.2.3", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@types/uuid": "^10.0.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.1", "eslint-plugin-import": "^2.32.0", "express": "^5.1.0", "fs-extra": "^11.3.0", "helmet": "^8.1.0", "jest": "^30.0.5", "module-alias": "^2.2.3", "mssql": "^11.0.1", "nestjs-zod": "^5.0.1", "pg": "^8.16.3", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "typeorm": "^0.3.25", "uuid": "^11.1.0", "winston": "^3.17.0", "yaml": "^2.8.0", "zod": "^4.1.7"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.32.0", "@nestjs/cli": "^11.0.8", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.1.5", "@swc/cli": "^0.7.8", "@swc/core": "^1.13.3", "@types/bcryptjs": "^3.0.0", "@types/cors": "^2.8.19", "@types/dotenv": "^8.2.3", "@types/eslint": "^9.6.1", "@types/express": "^5.0.3", "@types/fs-extra": "^11.0.4", "@types/helmet": "^4.0.0", "@types/http-status-codes": "^1.2.0", "@types/jest": "^30.0.0", "@types/node": "^24.1.0", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "concurrently": "^9.2.0", "eslint": "^9.32.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "git-cz": "^4.9.0", "globals": "^16.3.0", "husky": "^9.1.7", "jest": "^30.0.5", "lint-staged": "^16.1.2", "nodemon": "^3.1.10", "prettier": "^3.6.2", "supertest": "^7.1.4", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3", "typescript-eslint": "^8.38.0"}, "lint-staged": {"*.ts": ["prettier . --write"], "swagger.yaml": "npm run commands -- swagger"}, "config": {"commitizen": {"path": "@commitlint/cz-commitlint"}}}