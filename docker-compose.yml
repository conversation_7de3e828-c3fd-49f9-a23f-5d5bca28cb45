services:
  db:
    image: postgres:latest
    environment:
      - POSTGRES_PASSWORD:""
    ports:
      - 6543:6543
    volumes:
      - my-db-volume:/var/lib/postgresql/data
  app:
    container_name: node_app
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    ports:
      - '3000:3000'
    environment:
      - NODE_ENV=development
    env_file:
      - .env
    # volumes:
    #   - .:/usr/src/app
    #   - ./dist:/usr/src/app/dist
    #   - /usr/src/app/node_modules
    command: npm run start:dev
    restart: unless-stopped
    # networks: #this is used to configure container network connectivity
    #   - app-network
    # healthcheck: # Configures a health check to monitor the container's status via HTTP.
    #   test: ['CMD', 'curl', '-f', 'http://localhost:3000']
    #   interval: 10s # Time between each health check attempt.
    #   timeout: 5s  # Maximum time to wait for a response before marking it as failed.
    #   retries: 3  # Number of consecutive failures before the container is marked as unhealthy.
# networks:
#   app-network:
#     driver: bridge # Defines the custom bridge network used for container communication.
volumes:
  my-db-volume: