image: node:22-slim

pipelines:
  branches:
    RDT:
      - step:
          name: Build, linting, test & swagger generation.
          deployment: Test
          caches:
            - node
          script:
            - npm install
            - npm run build
            - npm run lint
            - npm run prettier
            - npm run test
            - npm run commands -- swagger
          # artifacts:
          #   - dist/**
          #   - .env
          #   - src/swagger/swagger.json
          variables:
            PORT: $PORT
            BASE_URL: $BASE_URL
            NODE_ENV: $NODE_ENV
            APP_VERSION: $APP_VERSION
            LOG_LEVEL: $LOG_LEVEL
            LOGGER_TIMESTAMP_FORMAT: $LOGGER_TIMESTAMP_FORMAT
            MASK_PROPERTIES: $MASK_PROPERTIES
            SWAGGER_ENDPOINT: $SWAGGER_ENDPOINT

      # Create & configure EC2, install & configure pm2 on server then enable below section and use artificats from above section.
      # - step:
      #     name: Deploy to AWS EC2
      #     deployment: Test
      #     caches:
      #       - node
      #     script:
      #       - pipe: atlassian/scp-deploy:1.4.1
      #         variables:
      #           USER: $EC2_USER
      #           SERVER: $EC2_HOST
      #           REMOTE_PATH: $EC2_REMOTE_PATH
      #           LOCAL_PATH: 'dist/*'
      #           SSH_KEY: $EC2_SSH_KEY
      #       - pipe: atlassian/ssh-run:0.4.0
      #         variables:
      #           SSH_USER: $EC2_USER
      #           SERVER: $EC2_HOST
      #           SSH_KEY: $EC2_SSH_KEY
      #           COMMAND: |
      #             cd $EC2_REMOTE_PATH
      #             pm2 restart ecosystem.config.js || node dist/index.js
