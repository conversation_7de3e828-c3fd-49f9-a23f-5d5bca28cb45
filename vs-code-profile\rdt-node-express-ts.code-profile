{"name": "rdt-node-express-ts", "icon": "settings-view-bar-icon", "settings": "{\"settings\":\"{\\n    \\\"git.autofetch\\\": true,\\n    \\\"[markdown]\\\": {\\n        \\\"editor.wordWrap\\\": \\\"on\\\"\\n    },\\n    \\\"[json]\\\": {\\n        \\\"editor.defaultFormatter\\\": \\\"esbenp.prettier-vscode\\\"\\n    },\\n    \\\"[jsonc]\\\": {\\n        \\\"editor.defaultFormatter\\\": \\\"vscode.json-language-features\\\"\\n    },\\n    \\\"[html]\\\": {\\n        \\\"editor.defaultFormatter\\\": \\\"esbenp.prettier-vscode\\\"\\n    },\\n    \\\"[javascript]\\\": {\\n        \\\"editor.defaultFormatter\\\": \\\"esbenp.prettier-vscode\\\"\\n    },\\n    \\\"[typescript]\\\": {\\n        \\\"editor.defaultFormatter\\\": \\\"esbenp.prettier-vscode\\\"\\n    },\\n    \\\"editor.formatOnSave\\\": true,\\n    \\\"editor.formatOnPaste\\\": true,\\n    \\\"editor.formatOnType\\\": true,\\n    \\\"files.autoSave\\\": \\\"afterDelay\\\",\\n    \\\"editor.tokenColorCustomizations\\\": {\\n        \\\"textMateRules\\\": [\\n            {\\n                \\\"scope\\\": \\\"keyword.other.dotenv\\\",\\n                \\\"settings\\\": {\\n                    \\\"foreground\\\": \\\"#FF000000\\\"\\n                }\\n            }\\n        ]\\n    },\\n    \\\"files.associations\\\": {\\n        \\\".env*\\\": \\\"dotenv\\\"\\n    },\\n    \\\"editor.tabSize\\\": 2,\\n    \\\"git.blame.editorDecoration.enabled\\\": true,\\n    \\\"git.branchProtection\\\": [\\n        \\\"master\\\",\\n        \\\"main\\\",\\n        \\\"staging\\\",\\n        \\\"uat\\\"\\n    ]\\n}\"}", "extensions": "[{\"identifier\":{\"id\":\"christian-kohler.npm-intellisense\",\"uuid\":\"************************************\"},\"displayName\":\"npm Intellisense\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"christian-kohler.path-intellisense\",\"uuid\":\"a41c1549-4053-44d4-bf30-60fc809b4a86\"},\"displayName\":\"Path Intellisense\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"dbaeumer.vscode-eslint\",\"uuid\":\"583b2b34-2c1e-4634-8c0b-0b82e283ea3a\"},\"displayName\":\"ESLint\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"donjayamanne.githistory\",\"uuid\":\"5960f38e-0bbe-4644-8f9c-9c8824e82511\"},\"displayName\":\"Git History\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"dotenv.dotenv-vscode\",\"uuid\":\"bfc47172-d830-4aab-9c7b-ecfdb1e97a29\"},\"displayName\":\"Dotenv Official +Vault\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"editorconfig.editorconfig\",\"uuid\":\"f60a60a6-95ba-42d4-b41c-3d24c1b89588\"},\"displayName\":\"EditorConfig for VS Code\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"esbenp.prettier-vscode\",\"uuid\":\"96fa4707-6983-4489-b7c5-d5ffdfdcce90\"},\"displayName\":\"Prettier - Code formatter\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"mechatroner.rainbow-csv\",\"uuid\":\"3792588c-3d35-442d-91ea-fe6a755e8155\"},\"displayName\":\"Rainbow CSV\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-azuretools.vscode-docker\",\"uuid\":\"************************************\"},\"displayName\":\"Docker\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode-remote.remote-containers\",\"uuid\":\"93ce222b-5f6f-49b7-9ab1-a0463c6238df\"},\"displayName\":\"Dev Containers\",\"preRelease\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"orta.vscode-jest\",\"uuid\":\"7997a8ae-1fae-4022-b834-12f6318aeffd\"},\"displayName\":\"Jest\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"postman.postman-for-vscode\",\"uuid\":\"48fcb244-71c2-4877-aed3-29b36098a411\"},\"displayName\":\"Postman\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"redhat.vscode-yaml\",\"uuid\":\"2061917f-f76a-458a-8da9-f162de22b97e\"},\"displayName\":\"YAML\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"waderyan.gitblame\",\"uuid\":\"2335b326-c334-4e81-bc51-c408fcec6e7c\"},\"displayName\":\"Git Blame\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"xabikos.javascriptsnippets\",\"uuid\":\"a2cec723-5349-460d-9de9-0fd1f8d3456f\"},\"displayName\":\"JavaScript (ES6) code snippets\",\"applicationScoped\":false}]", "globalState": "{\"storage\":{\"workbench.activity.pinnedViewlets2\":\"[{\\\"id\\\":\\\"workbench.view.explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":0},{\\\"id\\\":\\\"workbench.view.search\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":1},{\\\"id\\\":\\\"workbench.view.scm\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":2},{\\\"id\\\":\\\"workbench.view.debug\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":3},{\\\"id\\\":\\\"workbench.view.extension.npm\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.panel.aiSidebar\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":2},{\\\"id\\\":\\\"workbench.view.extension.bookmarks\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extensions\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":4},{\\\"id\\\":\\\"workbench.view.extension.azuresphere\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.dotenv\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.mongoDB\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.remote\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":4},{\\\"id\\\":\\\"workbench.view.extension.todo-tree-container\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":11},{\\\"id\\\":\\\"workbench.view.extension.test\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":6},{\\\"id\\\":\\\"workbench.view.extension.sidebar-panel\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.references-view\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":7},{\\\"id\\\":\\\"workbench.view.testing\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":5},{\\\"id\\\":\\\"workbench.view.extension.dockerView\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":9},{\\\"id\\\":\\\"workbench.view.extension.ts-playground\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.teamsfx\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":11},{\\\"id\\\":\\\"workbench.view.extension.ObjectScriptView\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":11},{\\\"id\\\":\\\"workbench.view.extension.frontmatter-explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":25},{\\\"id\\\":\\\"workbench.view.extension.openapi-explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":11},{\\\"id\\\":\\\"workbench.view.extension.copilot\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":11},{\\\"id\\\":\\\"workbench.view.extension.42crunch-platform\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":26},{\\\"id\\\":\\\"workbench.view.extension.thunder-client\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":12},{\\\"id\\\":\\\"workbench.view.extension.wallaby\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":12},{\\\"id\\\":\\\"workbench.view.extension.hexExplorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.github-actions\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":15},{\\\"id\\\":\\\"workbench.view.extension.jsonEditor-view\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":12},{\\\"id\\\":\\\"workbench.view.extension.copilot-sidebar-webview\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.stripe\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.objectExplorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":11},{\\\"id\\\":\\\"workbench.view.extension.browser-preview\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.PowerShellCommandExplorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":9},{\\\"id\\\":\\\"workbench.view.extension.nuget-explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.graalvm-explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.sqltoolsActivityBarContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":9},{\\\"id\\\":\\\"workbench.view.extension.sln_explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.github-pull-requests\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":9},{\\\"id\\\":\\\"workbench.view.extension.kubernetesView\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":11},{\\\"id\\\":\\\"workbench.view.extension.nx-console\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.node-notebook\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":9},{\\\"id\\\":\\\"workbench.view.extension.workspaceViewer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":9},{\\\"id\\\":\\\"workbench.view.extension.angular\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.cspell-explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.flutter\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.music-time\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.actions\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.powerShellProTools\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":14},{\\\"id\\\":\\\"workbench.view.extension.githd-explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.code-time\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":11},{\\\"id\\\":\\\"workbench.view.extension.platformio\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.vscode-edge-devtools-view\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":9},{\\\"id\\\":\\\"workbench.view.extension.github-pull-request\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.panel.interactiveSessionSidebar.copilot\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":100},{\\\"id\\\":\\\"workbench.view.extension.jupyter\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":9},{\\\"id\\\":\\\"workbench.view.extension.package-explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.project-manager\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":15},{\\\"id\\\":\\\"workbench.view.extension.cmake__viewContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.panel.markers\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":0},{\\\"id\\\":\\\"workbench.view.extension.data-wrangler-primary\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.azure\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.panel.terminal\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":22},{\\\"id\\\":\\\"workbench.view.extension.testExplorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":25},{\\\"id\\\":\\\"workbench.panel.repl\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":24},{\\\"id\\\":\\\"workbench.panel.output\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":25},{\\\"id\\\":\\\"workbench.view.extension.blockchain-explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":18},{\\\"id\\\":\\\"workbench.view.extension.CppRenameActivityBar\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":20},{\\\"id\\\":\\\"workbench.view.extension.VirtualGistsContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.opensshremotesexplorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":21},{\\\"id\\\":\\\"workbench.view.extension.l13Projects\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":20},{\\\"id\\\":\\\"workbench.view.extension.unotes\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":15},{\\\"id\\\":\\\"workbench.view.extension.latex\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":11},{\\\"id\\\":\\\"workbench.view.extension.todo\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":16},{\\\"id\\\":\\\"workbench.view.extension.pwa\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":25},{\\\"id\\\":\\\"workbench.view.extension.dataworkspace\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.liveshare\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.codestream-activitybar\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.copilot-labs\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.snippet-explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":15},{\\\"id\\\":\\\"workbench.view.extension.tree-view\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.1-kubernetesContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":11},{\\\"id\\\":\\\"workbench.view.extension.latex-workshop-activitybar\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":11},{\\\"id\\\":\\\"workbench.view.extension.favorites-bar\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":20},{\\\"id\\\":\\\"workbench.view.extension.log-viewer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":25},{\\\"id\\\":\\\"workbench.view.extension.favorites-explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":24},{\\\"id\\\":\\\"workbench.view.extension.gitlab-workflow\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.chatActivityViewSlack\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":17},{\\\"id\\\":\\\"workbench.view.extension.chatActivityViewDiscord\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":18},{\\\"id\\\":\\\"workbench.view.extension.appmap\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":12},{\\\"id\\\":\\\"workbench.view.extension.azdo-pull-requests\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":32},{\\\"id\\\":\\\"workbench.view.extension.2-cloudRunContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":12},{\\\"id\\\":\\\"workbench.view.extension.azure-policy\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":12},{\\\"id\\\":\\\"workbench.view.extension.thunder-client-sidebar-view\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":27},{\\\"id\\\":\\\"workbench.view.extension.snippView\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":29},{\\\"id\\\":\\\"workbench.view.extension.ionide-fsharp\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":29},{\\\"id\\\":\\\"workbench.view.extension.swaggerhub\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":29},{\\\"id\\\":\\\"workbench.view.extension.taskActivity\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":26},{\\\"id\\\":\\\"workbench.view.extension.gistpad\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":29},{\\\"id\\\":\\\"workbench.view.extension.VirtualRepositories\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.Repositories\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.VirtualRepositoriesContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":13},{\\\"id\\\":\\\"workbench.view.extension.3-cloudApiContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":13},{\\\"id\\\":\\\"workbench.view.extension.workspace-explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":28},{\\\"id\\\":\\\"workbench.view.extension.snippetsManager\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.iridium\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":19},{\\\"id\\\":\\\"workbench.view.extension.gitops\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":30},{\\\"id\\\":\\\"workbench.view.extension.4-cloudSecretsContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":14},{\\\"id\\\":\\\"workbench.view.extension.bloop-search-view\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":34},{\\\"id\\\":\\\"workbench.view.extension.microsoft-todo-unoffcial\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":29},{\\\"id\\\":\\\"workbench.view.extension.5-apigeeContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":15},{\\\"id\\\":\\\"workbench.view.extension.spring\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.editSessions\\\",\\\"pinned\\\":true,\\\"visible\\\":false},{\\\"id\\\":\\\"workbench.view.sync\\\",\\\"pinned\\\":true,\\\"visible\\\":false},{\\\"id\\\":\\\"userDataProfiles\\\",\\\"pinned\\\":true,\\\"visible\\\":true}]\",\"workbench.explorer.views.state.hidden\":\"[{\\\"id\\\":\\\"workbench.explorer.openEditorsView\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.explorer.fileView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"outline\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"timeline\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"npm\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.timeline\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.home\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.contributors\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"commitViewProvider\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"compareCommitViewProvider\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github:login\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"issues:github\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"pr:github\\\",\\\"isHidden\\\":false,\\\"order\\\":4},{\\\"id\\\":\\\"github:createPullRequest\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github:compareChanges\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"prStatus:github\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github:activePullRequest:welcome\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github:activePullRequest\\\",\\\"isHidden\\\":false,\\\"order\\\":9},{\\\"id\\\":\\\"workbench.explorer.emptyView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"couchbase\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"couchbase-iq\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"query-history\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github.cweijan.mysql\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github.dbclient.history\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github.cweijan.nosql\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.launchpad\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.drafts\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.workspaces\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.commitDetails\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.pullRequest\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.lineHistory\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.fileHistory\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.searchAndCompare\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.patchDetails\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"PowerShellCommands\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-couchbase.couchbase-query-workbench\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dbclient.search.result\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dbclinet.variable.config.view\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.graph\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.graphDetails\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"postman-for-vscode.sidebar-panel\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"postman-console-view\\\",\\\"isHidden\\\":false}]\",\"workbench.scm.views.state.hidden\":\"[{\\\"id\\\":\\\"workbench.scm.repositories\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.scm\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.repositories\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"gitlens.views.commits\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.commitDetails\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.fileHistory\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.lineHistory\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"gitlens.views.branches\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.remotes\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.stashes\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.tags\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.worktrees\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.searchAndCompare\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.scm.history\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.contributors\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"gitlens.views.scm.grouped\\\",\\\"isHidden\\\":false}]\",\"workbench.panel.repl.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.repl.view\\\",\\\"isHidden\\\":false}]\",\"workbench.panel.pinnedPanels\":\"[{\\\"id\\\":\\\"workbench.panel.markers\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":0},{\\\"id\\\":\\\"workbench.panel.output\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":1},{\\\"id\\\":\\\"workbench.panel.repl\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":2},{\\\"id\\\":\\\"~remote.forwardedPortsContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":5},{\\\"id\\\":\\\"workbench.panel.testResults\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":3},{\\\"id\\\":\\\"terminal\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":3},{\\\"id\\\":\\\"workbench.view.extension.sqltoolsPanelContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":6},{\\\"id\\\":\\\"workbench.view.extension.azurePanel\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":6},{\\\"id\\\":\\\"workbench.panel.comments\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.postman-console-view-container\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":6},{\\\"id\\\":\\\"workbench.view.extension.AskJarvisPanel\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.data-wrangler-secondary\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":6},{\\\"id\\\":\\\"workbench.view.extension.jupyter-variables\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":7},{\\\"id\\\":\\\"refactorPreview\\\",\\\"pinned\\\":true,\\\"visible\\\":false}]\",\"workbench.view.extensions.state.hidden\":\"[{\\\"id\\\":\\\"workbench.views.extensions.installed\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchOutdated\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.workspaceRecommendations\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.popular\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchRecentlyUpdated\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.otherRecommendations\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"extensions.recommendedList\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.enabled\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.views.extensions.disabled\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.views.extensions.marketplace\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchInstalled\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchEnabled\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchDisabled\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchBuiltin\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchWorkspaceUnsupported\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.builtinFeatureExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.builtinThemeExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.builtinProgrammingLanguageExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.untrustedUnsupportedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.untrustedPartiallySupportedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.virtualUnsupportedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.virtualPartiallySupportedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.deprecatedExtensions\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.dockerView.state.hidden\":\"[{\\\"id\\\":\\\"dockerContainers\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dockerImages\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dockerRegistries\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dockerNetworks\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dockerVolumes\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-docker.views.dockerContexts\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-docker.views.help\\\",\\\"isHidden\\\":false}]\",\"workbench.panel.alignment\":\"center\",\"workbench.panel.markers.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.markers.view\\\",\\\"isHidden\\\":false}]\",\"workbench.panel.output.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.output\\\",\\\"isHidden\\\":false}]\",\"terminal.hidden\":\"[{\\\"id\\\":\\\"terminal\\\",\\\"isHidden\\\":false}]\",\"workbench.view.search.state.hidden\":\"[{\\\"id\\\":\\\"workbench.view.search\\\",\\\"isHidden\\\":false}]\",\"workbench.view.debug.state.hidden\":\"[{\\\"id\\\":\\\"workbench.debug.welcome\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.variablesView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.watchExpressionsView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.callStackView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.loadedScriptsView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.breakPointsView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"jsBrowserBreakpoints\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"jsExcludedCallers\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"jsDebugNetworkTree\\\",\\\"isHidden\\\":false}]\",\"colorThemeData\":\"{\\\"id\\\":\\\"vs-dark vscode-theme-defaults-themes-dark_modern-json\\\",\\\"label\\\":\\\"Dark Modern\\\",\\\"settingsId\\\":\\\"Default Dark Modern\\\",\\\"themeTokenColors\\\":[{\\\"settings\\\":{\\\"foreground\\\":\\\"#D4D4D4\\\"},\\\"scope\\\":[\\\"meta.embedded\\\",\\\"source.groovy.embedded\\\",\\\"string meta.image.inline.markdown\\\",\\\"variable.legacy.builtin.python\\\"]},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\"},\\\"scope\\\":\\\"emphasis\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\"},\\\"scope\\\":\\\"strong\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#000080\\\"},\\\"scope\\\":\\\"header\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#6A9955\\\"},\\\"scope\\\":\\\"comment\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"constant.language\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#b5cea8\\\"},\\\"scope\\\":[\\\"constant.numeric\\\",\\\"variable.other.enummember\\\",\\\"keyword.operator.plus.exponent\\\",\\\"keyword.operator.minus.exponent\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#646695\\\"},\\\"scope\\\":\\\"constant.regexp\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"entity.name.tag\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d7ba7d\\\"},\\\"scope\\\":[\\\"entity.name.tag.css\\\",\\\"entity.name.tag.less\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#9cdcfe\\\"},\\\"scope\\\":\\\"entity.other.attribute-name\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d7ba7d\\\"},\\\"scope\\\":[\\\"entity.other.attribute-name.class.css\\\",\\\"source.css entity.other.attribute-name.class\\\",\\\"entity.other.attribute-name.id.css\\\",\\\"entity.other.attribute-name.parent-selector.css\\\",\\\"entity.other.attribute-name.parent.less\\\",\\\"source.css entity.other.attribute-name.pseudo-class\\\",\\\"entity.other.attribute-name.pseudo-element.css\\\",\\\"source.css.less entity.other.attribute-name.id\\\",\\\"entity.other.attribute-name.scss\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#f44747\\\"},\\\"scope\\\":\\\"invalid\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"underline\\\"},\\\"scope\\\":\\\"markup.underline\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\",\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"markup.bold\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\",\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"markup.heading\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\"},\\\"scope\\\":\\\"markup.italic\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"strikethrough\\\"},\\\"scope\\\":\\\"markup.strikethrough\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#b5cea8\\\"},\\\"scope\\\":\\\"markup.inserted\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ce9178\\\"},\\\"scope\\\":\\\"markup.deleted\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"markup.changed\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#6A9955\\\"},\\\"scope\\\":\\\"punctuation.definition.quote.begin.markdown\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#6796e6\\\"},\\\"scope\\\":\\\"punctuation.definition.list.begin.markdown\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ce9178\\\"},\\\"scope\\\":\\\"markup.inline.raw\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#808080\\\"},\\\"scope\\\":\\\"punctuation.definition.tag\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":[\\\"meta.preprocessor\\\",\\\"entity.name.function.preprocessor\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ce9178\\\"},\\\"scope\\\":\\\"meta.preprocessor.string\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#b5cea8\\\"},\\\"scope\\\":\\\"meta.preprocessor.numeric\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#9cdcfe\\\"},\\\"scope\\\":\\\"meta.structure.dictionary.key.python\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"meta.diff.header\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"storage\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"storage.type\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":[\\\"storage.modifier\\\",\\\"keyword.operator.noexcept\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ce9178\\\"},\\\"scope\\\":[\\\"string\\\",\\\"meta.embedded.assembly\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ce9178\\\"},\\\"scope\\\":\\\"string.tag\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ce9178\\\"},\\\"scope\\\":\\\"string.value\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d16969\\\"},\\\"scope\\\":\\\"string.regexp\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":[\\\"punctuation.definition.template-expression.begin\\\",\\\"punctuation.definition.template-expression.end\\\",\\\"punctuation.section.embedded\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d4d4d4\\\"},\\\"scope\\\":[\\\"meta.template.expression\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#9cdcfe\\\"},\\\"scope\\\":[\\\"support.type.vendored.property-name\\\",\\\"support.type.property-name\\\",\\\"source.css variable\\\",\\\"source.coffee.embedded\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"keyword\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"keyword.control\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d4d4d4\\\"},\\\"scope\\\":\\\"keyword.operator\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":[\\\"keyword.operator.new\\\",\\\"keyword.operator.expression\\\",\\\"keyword.operator.cast\\\",\\\"keyword.operator.sizeof\\\",\\\"keyword.operator.alignof\\\",\\\"keyword.operator.typeid\\\",\\\"keyword.operator.alignas\\\",\\\"keyword.operator.instanceof\\\",\\\"keyword.operator.logical.python\\\",\\\"keyword.operator.wordlike\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#b5cea8\\\"},\\\"scope\\\":\\\"keyword.other.unit\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":[\\\"punctuation.section.embedded.begin.php\\\",\\\"punctuation.section.embedded.end.php\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#9cdcfe\\\"},\\\"scope\\\":\\\"support.function.git-rebase\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#b5cea8\\\"},\\\"scope\\\":\\\"constant.sha.git-rebase\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d4d4d4\\\"},\\\"scope\\\":[\\\"storage.modifier.import.java\\\",\\\"variable.language.wildcard.java\\\",\\\"storage.modifier.package.java\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"variable.language\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#DCDCAA\\\"},\\\"scope\\\":[\\\"entity.name.function\\\",\\\"support.function\\\",\\\"support.constant.handlebars\\\",\\\"source.powershell variable.other.member\\\",\\\"entity.name.operator.custom-literal\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#4EC9B0\\\"},\\\"scope\\\":[\\\"support.class\\\",\\\"support.type\\\",\\\"entity.name.type\\\",\\\"entity.name.namespace\\\",\\\"entity.other.attribute\\\",\\\"entity.name.scope-resolution\\\",\\\"entity.name.class\\\",\\\"storage.type.numeric.go\\\",\\\"storage.type.byte.go\\\",\\\"storage.type.boolean.go\\\",\\\"storage.type.string.go\\\",\\\"storage.type.uintptr.go\\\",\\\"storage.type.error.go\\\",\\\"storage.type.rune.go\\\",\\\"storage.type.cs\\\",\\\"storage.type.generic.cs\\\",\\\"storage.type.modifier.cs\\\",\\\"storage.type.variable.cs\\\",\\\"storage.type.annotation.java\\\",\\\"storage.type.generic.java\\\",\\\"storage.type.java\\\",\\\"storage.type.object.array.java\\\",\\\"storage.type.primitive.array.java\\\",\\\"storage.type.primitive.java\\\",\\\"storage.type.token.java\\\",\\\"storage.type.groovy\\\",\\\"storage.type.annotation.groovy\\\",\\\"storage.type.parameters.groovy\\\",\\\"storage.type.generic.groovy\\\",\\\"storage.type.object.array.groovy\\\",\\\"storage.type.primitive.array.groovy\\\",\\\"storage.type.primitive.groovy\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#4EC9B0\\\"},\\\"scope\\\":[\\\"meta.type.cast.expr\\\",\\\"meta.type.new.expr\\\",\\\"support.constant.math\\\",\\\"support.constant.dom\\\",\\\"support.constant.json\\\",\\\"entity.other.inherited-class\\\",\\\"punctuation.separator.namespace.ruby\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#C586C0\\\"},\\\"scope\\\":[\\\"keyword.control\\\",\\\"source.cpp keyword.operator.new\\\",\\\"keyword.operator.delete\\\",\\\"keyword.other.using\\\",\\\"keyword.other.directive.using\\\",\\\"keyword.other.operator\\\",\\\"entity.name.operator\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#9CDCFE\\\"},\\\"scope\\\":[\\\"variable\\\",\\\"meta.definition.variable.name\\\",\\\"support.variable\\\",\\\"entity.name.variable\\\",\\\"constant.other.placeholder\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#4FC1FF\\\"},\\\"scope\\\":[\\\"variable.other.constant\\\",\\\"variable.other.enummember\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#9CDCFE\\\"},\\\"scope\\\":[\\\"meta.object-literal.key\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#CE9178\\\"},\\\"scope\\\":[\\\"support.constant.property-value\\\",\\\"support.constant.font-name\\\",\\\"support.constant.media-type\\\",\\\"support.constant.media\\\",\\\"constant.other.color.rgb-value\\\",\\\"constant.other.rgb-value\\\",\\\"support.constant.color\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#CE9178\\\"},\\\"scope\\\":[\\\"punctuation.definition.group.regexp\\\",\\\"punctuation.definition.group.assertion.regexp\\\",\\\"punctuation.definition.character-class.regexp\\\",\\\"punctuation.character.set.begin.regexp\\\",\\\"punctuation.character.set.end.regexp\\\",\\\"keyword.operator.negation.regexp\\\",\\\"support.other.parenthesis.regexp\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d16969\\\"},\\\"scope\\\":[\\\"constant.character.character-class.regexp\\\",\\\"constant.other.character-class.set.regexp\\\",\\\"constant.other.character-class.regexp\\\",\\\"constant.character.set.regexp\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#DCDCAA\\\"},\\\"scope\\\":[\\\"keyword.operator.or.regexp\\\",\\\"keyword.control.anchor.regexp\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d7ba7d\\\"},\\\"scope\\\":\\\"keyword.operator.quantifier.regexp\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":[\\\"constant.character\\\",\\\"constant.other.option\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d7ba7d\\\"},\\\"scope\\\":\\\"constant.character.escape\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#C8C8C8\\\"},\\\"scope\\\":\\\"entity.name.label\\\"}],\\\"semanticTokenRules\\\":[{\\\"_selector\\\":\\\"newOperator\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#d4d4d4\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"stringLiteral\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ce9178\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"customLiteral\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#d4d4d4\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"numberLiteral\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#b5cea8\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"newOperator\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#c586c0\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"stringLiteral\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ce9178\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"customLiteral\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#dcdcaa\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"numberLiteral\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#b5cea8\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}}],\\\"extensionData\\\":{\\\"_extensionId\\\":\\\"vscode.theme-defaults\\\",\\\"_extensionIsBuiltin\\\":true,\\\"_extensionName\\\":\\\"theme-defaults\\\",\\\"_extensionPublisher\\\":\\\"vscode\\\"},\\\"themeSemanticHighlighting\\\":true,\\\"colorMap\\\":{\\\"checkbox.border\\\":\\\"#3c3c3c\\\",\\\"editor.background\\\":\\\"#1f1f1f\\\",\\\"editor.foreground\\\":\\\"#cccccc\\\",\\\"editor.inactiveSelectionBackground\\\":\\\"#3a3d41\\\",\\\"editorIndentGuide.background1\\\":\\\"#404040\\\",\\\"editorIndentGuide.activeBackground1\\\":\\\"#707070\\\",\\\"editor.selectionHighlightBackground\\\":\\\"#add6ff26\\\",\\\"list.dropBackground\\\":\\\"#383b3d\\\",\\\"activityBarBadge.background\\\":\\\"#0078d4\\\",\\\"sideBarTitle.foreground\\\":\\\"#cccccc\\\",\\\"input.placeholderForeground\\\":\\\"#989898\\\",\\\"menu.background\\\":\\\"#1f1f1f\\\",\\\"menu.foreground\\\":\\\"#cccccc\\\",\\\"menu.separatorBackground\\\":\\\"#454545\\\",\\\"menu.border\\\":\\\"#454545\\\",\\\"menu.selectionBackground\\\":\\\"#0078d4\\\",\\\"statusBarItem.remoteForeground\\\":\\\"#ffffff\\\",\\\"statusBarItem.remoteBackground\\\":\\\"#0078d4\\\",\\\"ports.iconRunningProcessForeground\\\":\\\"#369432\\\",\\\"sideBarSectionHeader.background\\\":\\\"#181818\\\",\\\"sideBarSectionHeader.border\\\":\\\"#2b2b2b\\\",\\\"tab.selectedBackground\\\":\\\"#222222\\\",\\\"tab.selectedForeground\\\":\\\"#ffffffa0\\\",\\\"tab.lastPinnedBorder\\\":\\\"#cccccc33\\\",\\\"list.activeSelectionIconForeground\\\":\\\"#ffffff\\\",\\\"terminal.inactiveSelectionBackground\\\":\\\"#3a3d41\\\",\\\"widget.border\\\":\\\"#313131\\\",\\\"actionBar.toggledBackground\\\":\\\"#383a49\\\",\\\"activityBar.activeBorder\\\":\\\"#0078d4\\\",\\\"activityBar.background\\\":\\\"#181818\\\",\\\"activityBar.border\\\":\\\"#2b2b2b\\\",\\\"activityBar.foreground\\\":\\\"#d7d7d7\\\",\\\"activityBar.inactiveForeground\\\":\\\"#868686\\\",\\\"activityBarBadge.foreground\\\":\\\"#ffffff\\\",\\\"badge.background\\\":\\\"#616161\\\",\\\"badge.foreground\\\":\\\"#f8f8f8\\\",\\\"button.background\\\":\\\"#0078d4\\\",\\\"button.border\\\":\\\"#ffffff12\\\",\\\"button.foreground\\\":\\\"#ffffff\\\",\\\"button.hoverBackground\\\":\\\"#026ec1\\\",\\\"button.secondaryBackground\\\":\\\"#313131\\\",\\\"button.secondaryForeground\\\":\\\"#cccccc\\\",\\\"button.secondaryHoverBackground\\\":\\\"#3c3c3c\\\",\\\"chat.slashCommandBackground\\\":\\\"#34414b\\\",\\\"chat.slashCommandForeground\\\":\\\"#40a6ff\\\",\\\"chat.editedFileForeground\\\":\\\"#e2c08d\\\",\\\"checkbox.background\\\":\\\"#313131\\\",\\\"debugToolBar.background\\\":\\\"#181818\\\",\\\"descriptionForeground\\\":\\\"#9d9d9d\\\",\\\"dropdown.background\\\":\\\"#313131\\\",\\\"dropdown.border\\\":\\\"#3c3c3c\\\",\\\"dropdown.foreground\\\":\\\"#cccccc\\\",\\\"dropdown.listBackground\\\":\\\"#1f1f1f\\\",\\\"editor.findMatchBackground\\\":\\\"#9e6a03\\\",\\\"editorGroup.border\\\":\\\"#ffffff17\\\",\\\"editorGroupHeader.tabsBackground\\\":\\\"#181818\\\",\\\"editorGroupHeader.tabsBorder\\\":\\\"#2b2b2b\\\",\\\"editorGutter.addedBackground\\\":\\\"#2ea043\\\",\\\"editorGutter.deletedBackground\\\":\\\"#f85149\\\",\\\"editorGutter.modifiedBackground\\\":\\\"#0078d4\\\",\\\"editorLineNumber.activeForeground\\\":\\\"#cccccc\\\",\\\"editorLineNumber.foreground\\\":\\\"#6e7681\\\",\\\"editorOverviewRuler.border\\\":\\\"#010409\\\",\\\"editorWidget.background\\\":\\\"#202020\\\",\\\"errorForeground\\\":\\\"#f85149\\\",\\\"focusBorder\\\":\\\"#0078d4\\\",\\\"foreground\\\":\\\"#cccccc\\\",\\\"icon.foreground\\\":\\\"#cccccc\\\",\\\"input.background\\\":\\\"#313131\\\",\\\"input.border\\\":\\\"#3c3c3c\\\",\\\"input.foreground\\\":\\\"#cccccc\\\",\\\"inputOption.activeBackground\\\":\\\"#2489db82\\\",\\\"inputOption.activeBorder\\\":\\\"#2488db\\\",\\\"keybindingLabel.foreground\\\":\\\"#cccccc\\\",\\\"notificationCenterHeader.background\\\":\\\"#1f1f1f\\\",\\\"notificationCenterHeader.foreground\\\":\\\"#cccccc\\\",\\\"notifications.background\\\":\\\"#1f1f1f\\\",\\\"notifications.border\\\":\\\"#2b2b2b\\\",\\\"notifications.foreground\\\":\\\"#cccccc\\\",\\\"panel.background\\\":\\\"#181818\\\",\\\"panel.border\\\":\\\"#2b2b2b\\\",\\\"panelInput.border\\\":\\\"#2b2b2b\\\",\\\"panelTitle.activeBorder\\\":\\\"#0078d4\\\",\\\"panelTitle.activeForeground\\\":\\\"#cccccc\\\",\\\"panelTitle.inactiveForeground\\\":\\\"#9d9d9d\\\",\\\"peekViewEditor.background\\\":\\\"#1f1f1f\\\",\\\"peekViewEditor.matchHighlightBackground\\\":\\\"#bb800966\\\",\\\"peekViewResult.background\\\":\\\"#1f1f1f\\\",\\\"peekViewResult.matchHighlightBackground\\\":\\\"#bb800966\\\",\\\"pickerGroup.border\\\":\\\"#3c3c3c\\\",\\\"progressBar.background\\\":\\\"#0078d4\\\",\\\"quickInput.background\\\":\\\"#222222\\\",\\\"quickInput.foreground\\\":\\\"#cccccc\\\",\\\"settings.dropdownBackground\\\":\\\"#313131\\\",\\\"settings.dropdownBorder\\\":\\\"#3c3c3c\\\",\\\"settings.headerForeground\\\":\\\"#ffffff\\\",\\\"settings.modifiedItemIndicator\\\":\\\"#bb800966\\\",\\\"sideBar.background\\\":\\\"#181818\\\",\\\"sideBar.border\\\":\\\"#2b2b2b\\\",\\\"sideBar.foreground\\\":\\\"#cccccc\\\",\\\"sideBarSectionHeader.foreground\\\":\\\"#cccccc\\\",\\\"statusBar.background\\\":\\\"#181818\\\",\\\"statusBar.border\\\":\\\"#2b2b2b\\\",\\\"statusBar.debuggingBackground\\\":\\\"#0078d4\\\",\\\"statusBar.debuggingForeground\\\":\\\"#ffffff\\\",\\\"statusBar.focusBorder\\\":\\\"#0078d4\\\",\\\"statusBar.foreground\\\":\\\"#cccccc\\\",\\\"statusBar.noFolderBackground\\\":\\\"#1f1f1f\\\",\\\"statusBarItem.focusBorder\\\":\\\"#0078d4\\\",\\\"statusBarItem.prominentBackground\\\":\\\"#6e768166\\\",\\\"tab.activeBackground\\\":\\\"#1f1f1f\\\",\\\"tab.activeBorder\\\":\\\"#1f1f1f\\\",\\\"tab.activeBorderTop\\\":\\\"#0078d4\\\",\\\"tab.activeForeground\\\":\\\"#ffffff\\\",\\\"tab.selectedBorderTop\\\":\\\"#6caddf\\\",\\\"tab.border\\\":\\\"#2b2b2b\\\",\\\"tab.hoverBackground\\\":\\\"#1f1f1f\\\",\\\"tab.inactiveBackground\\\":\\\"#181818\\\",\\\"tab.inactiveForeground\\\":\\\"#9d9d9d\\\",\\\"tab.unfocusedActiveBorder\\\":\\\"#1f1f1f\\\",\\\"tab.unfocusedActiveBorderTop\\\":\\\"#2b2b2b\\\",\\\"tab.unfocusedHoverBackground\\\":\\\"#1f1f1f\\\",\\\"terminal.foreground\\\":\\\"#cccccc\\\",\\\"terminal.tab.activeBorder\\\":\\\"#0078d4\\\",\\\"textBlockQuote.background\\\":\\\"#2b2b2b\\\",\\\"textBlockQuote.border\\\":\\\"#616161\\\",\\\"textCodeBlock.background\\\":\\\"#2b2b2b\\\",\\\"textLink.activeForeground\\\":\\\"#4daafc\\\",\\\"textLink.foreground\\\":\\\"#4daafc\\\",\\\"textPreformat.foreground\\\":\\\"#d0d0d0\\\",\\\"textPreformat.background\\\":\\\"#3c3c3c\\\",\\\"textSeparator.foreground\\\":\\\"#21262d\\\",\\\"titleBar.activeBackground\\\":\\\"#181818\\\",\\\"titleBar.activeForeground\\\":\\\"#cccccc\\\",\\\"titleBar.border\\\":\\\"#2b2b2b\\\",\\\"titleBar.inactiveBackground\\\":\\\"#1f1f1f\\\",\\\"titleBar.inactiveForeground\\\":\\\"#9d9d9d\\\",\\\"welcomePage.tileBackground\\\":\\\"#2b2b2b\\\",\\\"welcomePage.progress.foreground\\\":\\\"#0078d4\\\"},\\\"watch\\\":false}\",\"workbench.telemetryOptOutShown\":\"true\",\"workbench.welcomePage.walkthroughMetadata\":\"[[\\\"ms-vscode.cpptools#cppWelcome\\\",{\\\"firstSeen\\\":1664821702677,\\\"stepIDs\\\":[\\\"verify.compiler.mac\\\",\\\"verify.compiler.linux\\\",\\\"verify.compiler.windows\\\",\\\"open.project\\\",\\\"setup.intellisense\\\",\\\"run.project.mac\\\",\\\"run.project.linux\\\",\\\"run.project.windows\\\",\\\"install.cmake.tools\\\",\\\"watch.tutorials\\\"],\\\"manaullyOpened\\\":false}],[\\\"vscode.typescript-language-features#nodejsWelcome\\\",{\\\"firstSeen\\\":1664960018008,\\\"stepIDs\\\":[\\\"vscode.typescript-language-features#nodejsWelcome#walkthroughs.nodejsWelcome.downloadNode.forMacOrWindows\\\",\\\"vscode.typescript-language-features#nodejsWelcome#walkthroughs.nodejsWelcome.downloadNode.forLinux\\\",\\\"vscode.typescript-language-features#nodejsWelcome#walkthroughs.nodejsWelcome.makeJsFile\\\",\\\"vscode.typescript-language-features#nodejsWelcome#walkthroughs.nodejsWelcome.debugJsFile\\\",\\\"vscode.typescript-language-features#nodejsWelcome#walkthroughs.nodejsWelcome.learnMoreAboutJs\\\"],\\\"manaullyOpened\\\":true}],[\\\"ms-python.python#pythonWelcome\\\",{\\\"firstSeen\\\":1681908470279,\\\"stepIDs\\\":[\\\"python.createPythonFile\\\",\\\"python.installPythonWin8\\\",\\\"python.installPythonMac\\\",\\\"python.installPythonLinux\\\",\\\"python.selectInterpreter\\\",\\\"python.createEnvironment\\\",\\\"python.runAndDebug\\\",\\\"python.learnMoreWithDS\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-python.python#pythonDataScienceWelcome\\\",{\\\"firstSeen\\\":1681908470283,\\\"stepIDs\\\":[\\\"python.installJupyterExt\\\",\\\"python.createNewNotebook\\\",\\\"python.openInteractiveWindow\\\",\\\"python.dataScienceLearnMore\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-toolsai.jupyter#jupyterWelcome\\\",{\\\"firstSeen\\\":1681908486067,\\\"stepIDs\\\":[\\\"ipynb.newUntitledIpynb\\\",\\\"jupyter.selectKernel\\\",\\\"jupyter.exploreAndDebug\\\",\\\"jupyter.dataScienceLearnMore\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-toolsai.datawrangler#dataWranglerWelcome\\\",{\\\"firstSeen\\\":1681908519464,\\\"stepIDs\\\":[\\\"openDataWrangler\\\",\\\"cleanDataUsingOperations\\\",\\\"applyYourChanges\\\",\\\"exportYourCode\\\",\\\"openTitanicCSV\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-azuretools.vscode-docker#dockerStart\\\",{\\\"firstSeen\\\":1681908838807,\\\"stepIDs\\\":[\\\"openFolder\\\",\\\"openFolderMac\\\",\\\"scaffold\\\",\\\"buildImage\\\",\\\"runContainer\\\",\\\"dockerExplorer\\\",\\\"pushImage\\\",\\\"azDeploy\\\",\\\"learn\\\"],\\\"manaullyOpened\\\":false}]]\",\"workbench.statusbar.hidden\":\"[\\\"status.workspaceTrust.57dcb8f9f5dc9d496c84d2c5aee0d197\\\",\\\"status.workspaceTrust.44afdfdf323dd08bfc0a2a33645ceef8\\\",\\\"status.workspaceTrust.c590d48d1c7085b9e6008078b96ca35e\\\",\\\"GitHub.copilot.status\\\"]\",\"memento/gettingStartedService\":\"{\\\"installGit\\\":{\\\"done\\\":true},\\\"commandPaletteTask\\\":{\\\"done\\\":true},\\\"commandPaletteTaskWeb\\\":{\\\"done\\\":true},\\\"ms-azuretools.vscode-docker#dockerStart#dockerExplorer\\\":{\\\"done\\\":true},\\\"copilotInstall\\\":{\\\"done\\\":true},\\\"quickOpen\\\":{\\\"done\\\":true},\\\"quickOpenWeb\\\":{\\\"done\\\":true},\\\"intellisense\\\":{\\\"done\\\":true},\\\"ms-python.python#pythonWelcome#python.createPythonFile\\\":{\\\"done\\\":true},\\\"ms-vscode.powershell#PowerShell#file\\\":{\\\"done\\\":true},\\\"ms-vscode-remote.remote-wsl#wslWalkthrough#create.project\\\":{\\\"done\\\":true},\\\"ms-vscode-remote.remote-wsl#wslWalkthrough#explore.commands\\\":{\\\"done\\\":true},\\\"ms-azuretools.vscode-docker#dockerStart#openFolder\\\":{\\\"done\\\":true}}\",\"commandPalette.mru.cache\":\"{\\\"usesLRU\\\":true,\\\"entries\\\":[{\\\"key\\\":\\\"extension.pgsql.connect\\\",\\\"value\\\":1},{\\\"key\\\":\\\"json\\\",\\\"value\\\":2},{\\\"key\\\":\\\"npm-intellisense.import\\\",\\\"value\\\":5}]}\",\"commandPalette.mru.counter\":\"6\",\"userDataProfiles.state.hidden\":\"[{\\\"id\\\":\\\"workbench.views.profiles.export.preview\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.profiles.import.preview\\\",\\\"isHidden\\\":false}]\",\"workbench.view.remote.state.hidden\":\"[{\\\"id\\\":\\\"targetsContainers\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"detailsContainers\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"devVolumes\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"~remote.helpPanel\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"remoteTargets\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.gitlens.state.hidden\":\"[{\\\"id\\\":\\\"gitlens.views.home\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.contributors\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.launchpad\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.drafts\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.workspaces\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.gitlensPanel.state.hidden\":\"[{\\\"id\\\":\\\"gitlens.views.timeline\\\",\\\"isHidden\\\":false}]\",\"remote.explorerType\":\"dev-container,attached-container,exec,k8s-container\",\"workbench.views.service.auxiliarybar.9c5197b2-52b7-4398-8b4b-f98b2edea621.state.hidden\":\"[{\\\"id\\\":\\\"timeline\\\",\\\"isHidden\\\":false}]\",\"workbench.auxiliarybar.pinnedPanels\":\"[{\\\"id\\\":\\\"workbench.panel.chat\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":100},{\\\"id\\\":\\\"workbench.panel.chatEditing\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":101}]\",\"workbench.views.service.sidebar.3c2bac6e-e353-45bb-b9d3-d52c07d757fc.state.hidden\":\"[{\\\"id\\\":\\\"pr:github\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.github-pull-requests.state.hidden\":\"[{\\\"id\\\":\\\"github:login\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"pr:github\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"issues:github\\\",\\\"isHidden\\\":false}]\",\"workbench.views.service.sidebar.5f49c66d-a31f-4c5b-a6bb-3dcf75b88035.state.hidden\":\"[{\\\"id\\\":\\\"github:activePullRequest\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.github-pull-request.state.hidden\":\"[{\\\"id\\\":\\\"github:createPullRequest\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github:compareChanges\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"prStatus:github\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github:activePullRequest\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github:activePullRequest:welcome\\\",\\\"isHidden\\\":false}]\",\"workbench.panel.interactiveSessionSidebar.copilot.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.interactiveSession.view.copilot\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.test.state.hidden\":\"[{\\\"id\\\":\\\"workbench.view.testing\\\",\\\"isHidden\\\":false}]\",\"workbench.panel.chat.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.chat.view.copilot\\\",\\\"isHidden\\\":false}]\",\"workbench.panel.chatEditing.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.chat.view.edits\\\",\\\"isHidden\\\":false}]\",\"workbench.activityBar.location\":\"default\",\"~remote.forwardedPortsContainer.hidden\":\"[{\\\"id\\\":\\\"~remote.forwardedPorts\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.gitlensInspect.state.hidden\":\"[{\\\"id\\\":\\\"gitlens.views.commitDetails\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.pullRequest\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.lineHistory\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.fileHistory\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.timeline\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.searchAndCompare\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.sidebar-panel.state.hidden\":\"[{\\\"id\\\":\\\"postman-for-vscode.sidebar-panel\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.dotenv.state.hidden\":\"[{\\\"id\\\":\\\"dotenv.view.vault\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.postman-console-view-container.state.hidden\":\"[{\\\"id\\\":\\\"postman-console-view\\\",\\\"isHidden\\\":false}]\"}}"}