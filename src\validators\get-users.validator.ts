import { z } from 'zod';

const dateStringToDate = () =>
  z.preprocess((arg) => {
    if (typeof arg === 'string' || arg instanceof Date) return new Date(arg);
    return undefined;
  }, z.date().optional());

export const GetUsersValidator = z.object({
  id: z.number().optional(),
  username: z.string().min(3).optional(),
  email: z.email().optional(),
  createdAt: dateStringToDate(),
  updatedAt: dateStringToDate(),
  searchKey: z.string().optional(),
});
