import dotenv from 'dotenv';
import type { DataSourceOptions } from 'typeorm';
dotenv.config();

enum Dialect {
  MYSQL = 'mysql',
  POSTGRES = 'postgres',
  SQLITE = 'sqlite',
  MARIADB = 'mariadb',
  MSSQL = 'mssql',
}

export const dataSourceOptions: DataSourceOptions = {
  type: process.env.DATABASE_DIALECT as Dialect,
  host: process.env.DATABASE_HOST || 'localhost',
  port: Number(process.env.DATABASE_PORT) || 5432,
  username: process.env.DATABASE_USERNAME || 'test',
  password: process.env.DATABASE_PASSWORD || 'test',
  database: process.env.DATABASE_DATABASE || 'test',
  logging: process.env.DATABASE_LOGGING === 'true' || false,
  entities: [__dirname + '/../**/*.model.{js,ts}'],
  migrations: [__dirname + '/../**/migrations/*.{js,ts}'],
  migrationsTableName: 'migrations',
  synchronize: process.env.NODE_ENV === 'development', // Only sync in development
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  // Connection pool settings for better performance
  extra: {
    connectionLimit: 10,
    acquireTimeout: 60000,
    timeout: 60000,
  },
};
