import { commonConfig } from '@configs';
import type { SwaggerInterface } from '@interfaces';
import fs from 'fs-extra';
import path from 'path';
import yaml from 'yaml';
import { LoggerService } from '../services/logger.service';

const mainFilePath = 'main.swagger.yaml';
const sourceDir = './src/swagger/';
const includeFileTypes = '.swagger.yaml';
const outputFilePath = 'swagger.yaml';
const loggerService = new LoggerService();

/**
 * Merge all the path yaml files into one.
 */

export const swagger = async (): Promise<void> => {
  try {
    // Read and parse the main YAML file
    const mainYaml = yaml.parse(await fs.readFile(path.join(sourceDir, mainFilePath), 'utf8')) as SwaggerInterface;

    // Read all files from the source directory
    const files = fs.readdirSync(sourceDir).filter(
      (file: string) =>
        // read all except of main and merged file.
        file.endsWith(includeFileTypes) && !file.startsWith(mainFilePath) && !file.startsWith(outputFilePath),
    );

    mainYaml.components = mainYaml.components ?? {};
    mainYaml.components.schemas = mainYaml.components.schemas ?? {};

    for (const file of files) {
      const filePath = path.join(sourceDir, file);
      const fileContent = yaml.parse(await fs.readFile(filePath, 'utf8')) as SwaggerInterface;

      // Merge paths
      if (fileContent.paths) {
        mainYaml.paths = { ...mainYaml.paths, ...fileContent.paths };
      }

      // Merge components
      if (fileContent.components?.schemas) {
        mainYaml.components.schemas = { ...mainYaml.components.schemas, ...fileContent.components.schemas };
      }
    }

    // Set base URLs
    const servers: [{ url?: string; description?: string }] = [
      {
        url: commonConfig.baseUrl,
        description: commonConfig.nodeEnv,
      },
    ];
    mainYaml.servers?.forEach((item) => {
      if (item.url) {
        servers.push({ url: item.url, description: item.description });
      }
    });
    mainYaml.servers = servers;

    // Convert merged content back to YAML and save
    await fs.writeFile(path.join(sourceDir, outputFilePath), yaml.stringify(mainYaml), 'utf8');
    loggerService.info(`✅ Merged YAML saved as ${outputFilePath}`);
  } catch (error) {
    loggerService.error('❌ Error merging YAML files:');
    loggerService.error(error);
  }
};
