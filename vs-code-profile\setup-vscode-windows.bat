@echo off
SETLOCAL ENABLEDELAYEDEXPANSION

REM === Name of the VS Code profile to use
SET PROFILE_NAME=rdt-node-express-ts

REM === Find the root of the project (this batch file's grandparent folder)
SET SCRIPT_DIR=%~dp0
CD /D "%SCRIPT_DIR%\.."

REM === Open VS Code in the project root with the profile
echo [INFO] Opening VS Code in "%CD%" with profile "%PROFILE_NAME%"...
code . --profile "%PROFILE_NAME%"

ENDLOCAL
