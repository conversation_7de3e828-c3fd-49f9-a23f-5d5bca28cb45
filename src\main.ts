import { attachRequestId, ExceptionMiddleware } from '@middlewares';
import { NestFactory } from '@nestjs/core';
import 'module-alias/register';
import { AppModule } from './app.module';
// import { TracerPlugin } from '@plugins/tracer';
import { expressSwagger } from '@plugins';
import cors from 'cors';
import express from 'express';
import helmet from 'helmet';
async function bootstrap(): Promise<void> {
  const app = await NestFactory.create(AppModule);
  app.use(express.json());
  app.use(express.urlencoded({ extended: true }));
  app.use(cors());
  app.use(helmet());
  app.use(expressSwagger);

  app.use(attachRequestId);

  app.useGlobalFilters(new ExceptionMiddleware());
  await app.listen(process.env.PORT ?? 3000);
}
void bootstrap();
