// import 'module-alias/register';
// import { commonConfig } from '@configs/common';
// import { exceptionMiddleware } from '@middlewares/exception';
// import { attachRequestId } from '@middlewares/logger';
// import { DatabasePlugin } from '@plugins/database';
// // import { TracerPlugin } from '@plugins/tracer';
// import { expressSwagger } from '@plugins/swagger';
// import { indexRouter } from '@routes/index';
// import { LoggerService } from './services/logger.service';
// import cors from 'cors';
// import express, { type Express } from 'express';
// import helmet from 'helmet';

// const app: Express = express();

// app.use(express.json());
// app.use(express.urlencoded({ extended: true }));
// app.use(cors());
// app.use(helmet());
// app.use(expressSwagger);

// app.use(attachRequestId);
// app.use('/', indexRouter);
// app.use(exceptionMiddleware);

// const start = async (): Promise<void> => {
//   const logger = new LoggerService();
//   // const tracer = new TracerPlugin();
//   const database = new DatabasePlugin();

//   try {
//     // tracer.start();
//     await database.connect();

//     // start server.
//     app.listen(commonConfig.port, () => {
//       logger.info(`[server]: Server is running at ${commonConfig.baseUrl}`);
//     });
//   } catch (error) {
//     // await tracer.stop();
//     await database.disconnect();
//     logger.error(error);
//     process.exit(1);
//   }
// };

// void start();
