import { LogLevel } from '@enums';
import dotenv from 'dotenv';
dotenv.config();

interface LoggerConfig {
  appVersion: string;
  logLevel: string;
  logsReadableFormat: number;
  timestampFormat: string;
  maskProperties: string[];
  maskedValue: string;
}

export const loggerConfig: LoggerConfig = {
  appVersion: process.env.APP_VERSION || '1.0.0.',
  logLevel: process.env.LOG_LEVEL || LogLevel.debug,
  logsReadableFormat: Number(process.env.LOGS_READABLE_FORMAT) || 0,
  timestampFormat: process.env.LOGGER_TIMESTAMP_FORMAT || 'YYYY-MM-DD HH:mm:ss',
  maskProperties: (process.env.MASK_PROPERTIES || 'password').split(','),
  maskedValue: '*****',
};
