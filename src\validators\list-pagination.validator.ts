import { commonConfig } from '@configs';
import { SearchDirection } from '@enums';
import { z } from 'zod';
import { GetUsersValidator } from './get-users.validator';

export const listPaginationQuery = GetUsersValidator.extend({
  limit: z
    .number()
    .int()
    .min(1)
    .max(commonConfig.pagination.maxLimit)
    .optional()
    .default(commonConfig.pagination.default),

  page: z.number().int().min(1).optional().default(1),

  sortOrder: z
    .enum(Object.values(SearchDirection) as [string, ...string[]])
    .optional()
    .default(SearchDirection.ASC),

  sortField: z.string().optional().default('id'),
});

export type ListPaginationQuery = z.infer<typeof listPaginationQuery>;
