import { PipeTransform, Injectable, BadRequestException, ArgumentMetadata } from '@nestjs/common';
import { ZodError, ZodSchema } from 'zod';

@Injectable()
export class ZodValidatorPipe implements PipeTransform {
  constructor(private schema: ZodSchema) {}

  transform(value: unknown, _metadata: ArgumentMetadata) {
    const result = this.schema.safeParse(value);

    if (!result.success) {
      throw new BadRequestException(this.formatErrors(result.error).join(', '));
    }

    return result.data;
  }

  private formatErrors(error: ZodError) {
    return error.issues.map((issue) => issue.message);
  }

  /**
   * Factory method to return a new pipe instance
   * @param schema Zod schema
   */
  static with(schema: ZodSchema): ZodValidatorPipe {
    return new ZodValidatorPipe(schema);
  }
}
