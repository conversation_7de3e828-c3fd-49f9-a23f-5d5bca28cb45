import type { RequestInterface } from '@interfaces';
import { AsyncLocalStorage } from 'async_hooks';
import type { NextFunction, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';

export const requestIdStorage = new AsyncLocalStorage<string>();

/**
 * Attaching unique uuid to the request and storing it to async local storage for consistency.
 *
 * @param req
 * @param _res
 * @param next
 */
export const attachRequestId = (req: RequestInterface, _res: Response, next: NextFunction): void => {
  const requestId = uuidv4();
  req.requestId = requestId;

  // Do not add large data to AsyncLocalStorage as it can impact on performance.
  requestIdStorage.run(requestId, () => {
    next();
  });
};
