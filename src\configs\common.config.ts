import dotenv from 'dotenv';
dotenv.config();

interface CommonConfig {
  port: number;
  baseUrl: string;
  nodeEnv: string;
  pagination: {
    default: number;
    maxLimit: number;
  };
}

export const commonConfig: CommonConfig = {
  port: Number(process.env.PORT) || 3000,
  baseUrl: process.env.BASE_URL ?? '',
  nodeEnv: process.env.NODE_ENV ?? 'development',
  pagination: {
    default: Number(process.env.DEFAULT_PAGINATION_LIMIT ?? 10),
    maxLimit: Number(process.env.MAX_PAGINATION_LIMIT ?? 100),
  },
};
