import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, HttpStatus } from '@nestjs/common';
import request from 'supertest';
import { AppModule } from '../src/app.module';
import { ExceptionMiddleware } from '@middlewares';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import { User } from '@models';

describe('UsersController (full coverage)', () => {
  jest.setTimeout(20000);
  let app: INestApplication;
  let userRepository: Repository<User>;
  let createdUserId: number;
  const mockUser = {
    id: 1,
    username: 'testuser',
    email: '<EMAIL>',
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalFilters(new ExceptionMiddleware());
    await app.init();

    userRepository = moduleFixture.get<Repository<User>>(getRepositoryToken(User));
    await userRepository.save([
      { username: 'Test1', email: '<EMAIL>', password: 'test1' },
      { username: 'Test2', email: '<EMAIL>', password: 'test2' },
    ]);
  });

  afterAll(async () => {
    await app.close();
  });

  it('POST /users should return success response', async () => {
    const res = await request(app.getHttpServer())
      .post('/users')
      .send({ username: 'testuser', email: '<EMAIL>', password: 'test-password' })
      .expect(HttpStatus.OK)
      .expect((res) => {
        expect(res.body.success).toBe(true);
        expect(res.body.data.user).toMatchObject({
          username: mockUser.username,
          email: mockUser.email,
        });
      });
    createdUserId = res.body.data.user.id;
    expect(createdUserId).toBeDefined();
  });

  it('POST /users should return error response for invalid body', async () => {
    await request(app.getHttpServer())
      .post('/users')
      .send({ username: 't', email: 'test@example', password: 'test' })
      .expect(HttpStatus.BAD_REQUEST)
      .expect((res) => {
        expect(res.body.success).toBe(false);
        expect(res.body.message).toBe(
          'Username must be at least 2 characters long, Invalid email, Password must be at least 6 characters long',
        );
      });
  });

  it('POST /users should return error response for required fields not present in the body', async () => {
    await request(app.getHttpServer())
      .post('/users')
      .send({ email: '<EMAIL>', password: 'testPassword' })
      .expect(HttpStatus.BAD_REQUEST)
      .expect((res) => {
        expect(res.body.success).toBe(false);
        expect(res.body.message).toBe('Username is required');
      });
  });

  it('PUT /users/:id should handle update success', () => {
    return request(app.getHttpServer())
      .put(`/users/${createdUserId}`)
      .send({ username: 'updated', email: '<EMAIL>', password: 'test-password' })
      .expect(HttpStatus.OK)
      .expect((res) => {
        expect(res.body.data.user).toMatchObject({
          username: 'updated',
          email: mockUser.email,
        });
        expect(res.body.message).toContain('updated');
      });
  });

  it('PUT /users with incorrect id should return error via exception middleware', () => {
    return request(app.getHttpServer())
      .put('/users/9999')
      .send({ username: 'irrelevant', email: '<EMAIL>', password: 'testPassword' })
      .expect(HttpStatus.NOT_FOUND)
      .expect((res) => {
        expect(res.body.success).toBe(false);
        expect(res.body.message).toEqual('User data not found');
      });
  });

  it('PUT /users with invalid id should return error via exception middleware', () => {
    return request(app.getHttpServer())
      .put('/users/-99')
      .send({ username: 'irrelevant', email: '<EMAIL>', password: 'testPassword' })
      .expect(HttpStatus.BAD_REQUEST)
      .expect((res) => {
        expect(res.body.success).toBe(false);
        expect(res.body.message).toEqual('Invalid id');
      });
  });

  it('GET /users should fetch users list successfully', () => {
    return request(app.getHttpServer())
      .get('/users')
      .expect(HttpStatus.OK)
      .expect((res) => {
        expect(res.body.data.users.length).toBeGreaterThanOrEqual(1);
        expect(res.body.data.metadata.page).toBe(1);
      });
  });

  it('GET /users/:id should fetch correct user', () => {
    return request(app.getHttpServer())
      .get(`/users/${createdUserId}`)
      .expect(HttpStatus.OK)
      .expect((res) => {
        expect(res.body.data.users.length).toBe(1);
        expect(res.body.data.users[0].email).toBe(mockUser.email);
      });
  });

  it('GET /users/:id with invalid id should return error response', () => {
    return request(app.getHttpServer())
      .get(`/users/-99`)
      .expect(HttpStatus.BAD_REQUEST)
      .expect((res) => {
        expect(res.body.success).toBe(false);
        expect(res.body.message).toBe('Invalid id');
      });
  });

  it('DELETE /users/:id should delete the user successfully', () => {
    return request(app.getHttpServer())
      .delete(`/users/${createdUserId}`)
      .expect(HttpStatus.OK)
      .expect((res) => {
        expect(res.body.message).toBe('User deleted successfully.');
        expect(res.body.data.user.affected).toBe(1);
      });
  });

  it('DELETE /users with incorrect id should return error via exception middleware', () => {
    return request(app.getHttpServer())
      .delete('/users/9999')
      .expect(HttpStatus.NOT_FOUND)
      .expect((res) => {
        expect(res.body.success).toBe(false);
        expect(res.body.message).toEqual('User data not found');
      });
  });

  it('DELETE /users with invalid id should return error via exception middleware', () => {
    return request(app.getHttpServer())
      .delete('/users/-99')
      .expect(HttpStatus.BAD_REQUEST)
      .expect((res) => {
        expect(res.body.success).toBe(false);
        expect(res.body.message).toEqual('Invalid id');
      });
  });
});
