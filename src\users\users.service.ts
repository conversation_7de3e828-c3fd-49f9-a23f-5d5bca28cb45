import { SearchDirection } from '@enums';
import { UserFilterInterface } from '@interfaces';
import { User } from '@models';
import { Injectable, NotFoundException } from '@nestjs/common';
import { UserRepository } from '@repository';
import { LoggerService } from '../services/logger.service';
import { DeleteResult } from 'typeorm';
import { CreateUserDTO, UpdateUserDTO } from '@validators';

@Injectable()
export class UsersService {
  constructor(
    private readonly logger: LoggerService,
    private readonly userRepository: UserRepository,
  ) {}
  //   private readonly logger = new LoggerService(this.constructor.name);
  //   private readonly userRepository = new UserRepository();

  /**
   *
   * @param filter UserFilterInterface
   * @returns
   */
  async findAllUsers(filter: UserFilterInterface): Promise<[User[], number]> {
    const users = await this.userRepository.findUsersByFilter(filter);
    this.logger.info('Found Users:', { users });

    return users;
  }

  /**
   *
   * @param id number
   * @returns
   */
  async findUserById(id: number): Promise<User> {
    const filter: UserFilterInterface = {
      id,
      pagination: {
        page: 1,
        limit: 1,
        sortOrder: SearchDirection.ASC,
        sortField: 'id',
      },
    };
    const [users, userCount] = await this.findAllUsers(filter);

    if (userCount === 0) {
      this.logger.error(`User not found with id => ${id}`);
      throw new NotFoundException('User data not found');
    }

    return users[0];
  }

  /**
   *
   * @param email string
   * @returns
   */
  async findUserByEmail(email: string): Promise<User> {
    const filter: UserFilterInterface = {
      email,
      pagination: {
        page: 1,
        limit: 1,
        sortOrder: SearchDirection.ASC,
        sortField: 'id',
      },
    };
    const [users, userCount] = await this.findAllUsers(filter);

    if (userCount === 0) {
      throw new NotFoundException('User data not found');
    }

    return users[0];
  }

  /**
   *
   * @param userDetails UserEntityInterface
   * @returns
   */
  async createUser(userDetails: CreateUserDTO): Promise<User> {
    userDetails.createdAt = new Date();
    userDetails.updatedAt = new Date();
    const user = await this.userRepository.saveUser(userDetails);
    this.logger.info('User created:', { user });

    return user;
  }

  /**
   *
   * @param userDetails UserEntityInterface
   * @returns
   */
  async updateUser(userDetails: UpdateUserDTO, id: number): Promise<User> {
    const userInfo = await this.findUserById(id);

    userInfo.username = userDetails.username;
    userInfo.email = userDetails.email;
    userInfo.password = userDetails.password;
    userInfo.updatedAt = new Date();
    const user = await this.userRepository.saveUser(userInfo);
    this.logger.info('User updated:', { user });

    return user;
  }

  async deleteUser(id: number): Promise<DeleteResult> {
    await this.findUserById(id);

    const userDeleted = await this.userRepository.deleteUser(id);
    this.logger.info('User deleted:', { userDeleted });

    return userDeleted;
  }
}
