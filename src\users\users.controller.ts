import { SearchDirection } from '@enums';
import { CommonResponseInterface, RequestInterface, UserFilterInterface } from '@interfaces';
import { Body, Controller, Delete, Get, HttpStatus, Param, Post, Put, Query, Req, Res } from '@nestjs/common';
import {
  CreateUserSchema,
  CreateUserDTO,
  UpdateUserSchema,
  UpdateUserDTO,
  UserIdParamSchema,
  UserIdParams,
  ListPaginationQuery,
  listPaginationQuery,
  ZodValidatorPipe,
} from '@validators';
import { Response } from 'express';
import { UsersService } from './users.service';
import { LoggerService } from '@services';

@Controller('users')
export class UsersController {
  constructor(
    private readonly _userService: UsersService,
    private readonly logger: LoggerService,
  ) {}

  @Get('/:id')
  async getUser(
    @Param(ZodValidatorPipe.with(UserIdParamSchema)) params: UserIdParams,
    @Query(ZodValidatorPipe.with(listPaginationQuery)) query: ListPaginationQuery,
  ): Promise<CommonResponseInterface> {
    try {
      const usersFilters: UserFilterInterface = {
        id: params.id,
        pagination: {
          limit: Number(query.limit) || 10,
          page: Number(query.page) || 1,
          sortOrder: (query.sortOrder as SearchDirection | undefined) ?? SearchDirection.ASC,
          sortField: (query.sortField as string) || 'id',
        },
      };

      const [users, totalUsers] = await this._userService.findAllUsers(usersFilters);

      return {
        success: true,
        message: 'Users fetched successfully.',
        data: {
          totalUsers,
          users,
          metadata: {
            page: usersFilters.pagination.page,
            limit: usersFilters.pagination.limit,
          },
        },
      };
    } catch (error: unknown) {
      console.log(error);
      throw error;
    }
  }

  // TODO: Fix fetch all users as mentioned in routes
  @Get()
  async fetchUsers(
    @Req() _req: RequestInterface,
    @Res() res: Response,
    @Query(ZodValidatorPipe.with(listPaginationQuery)) query: ListPaginationQuery,
  ): Promise<void> {
    try {
      const usersFilters: UserFilterInterface = {
        ...query,
        pagination: {
          limit: Number(query.limit) || 10,
          page: Number(query.page) || 1,
          sortOrder: (query.sortOrder as SearchDirection | undefined) ?? SearchDirection.ASC,
          sortField: (query.sortField as string) || 'id',
        },
      };

      const [users, totalUsers] = await this._userService.findAllUsers(usersFilters);

      const response: CommonResponseInterface = {
        success: true,
        message: 'Users fetched successfully.',
        data: {
          totalUsers,
          users,
          metadata: {
            page: usersFilters.pagination.page,
            limit: usersFilters.pagination.limit,
          },
        },
      };

      res.status(HttpStatus.OK).json(response);
    } catch (error: unknown) {
      console.log(error);
      throw error;
    }
  }

  @Post()
  async createUser(
    @Req() _req: RequestInterface,
    @Res() res: Response,
    @Body(ZodValidatorPipe.with(CreateUserSchema)) body: CreateUserDTO,
  ): Promise<void> {
    try {
      const user = await this._userService.createUser(body);

      const response: CommonResponseInterface = {
        success: true,
        message: 'User created successfully.',
        data: {
          user,
        },
      };

      res.status(HttpStatus.OK).json(response);
    } catch (error: unknown) {
      console.log(error);
      throw error;
    }
  }

  @Put('/:id')
  async updateUser(
    @Body(ZodValidatorPipe.with(UpdateUserSchema)) body: UpdateUserDTO,
    @Param(ZodValidatorPipe.with(UserIdParamSchema)) params: UserIdParams,
  ): Promise<CommonResponseInterface> {
    try {
      const user = await this._userService.updateUser(body, params.id);
      return {
        success: true,
        message: 'User updated successfully.',
        data: {
          user,
        },
      };
    } catch (error: unknown) {
      console.log(error);
      throw error;
    }
  }

  @Delete('/:id')
  async deleteUser(
    @Param(ZodValidatorPipe.with(UserIdParamSchema)) params: UserIdParams,
  ): Promise<CommonResponseInterface> {
    try {
      const user = await this._userService.deleteUser(params.id);

      return {
        success: true,
        message: 'User deleted successfully.',
        data: {
          user,
        },
      };
    } catch (error: unknown) {
      this.logger.error(error);
      throw error;
    }
  }
}
