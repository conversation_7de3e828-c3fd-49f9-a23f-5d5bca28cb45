import { SearchDirection } from '@enums';
import { UserEntityInterface, UserFilterInterface } from '@interfaces';
import { NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { UserRepository } from '@repository';
import { LoggerService } from '@services';
import { UsersService } from '../src/users/users.service';

describe('UsersService', () => {
  let service: UsersService;
  let mockUserRepository = {
    findUsersByFilter: jest.fn(),
    saveUser: jest.fn(),
    deleteUser: jest.fn(),
  };
  let mockLogger: { info: jest.Mock };

  let mockUser: UserEntityInterface = {
    id: 1,
    username: 'john',
    email: '<EMAIL>',
    password: 'plain',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: LoggerService,
          useValue: {
            info: jest.fn(),
            error: jest.fn(),
          },
        },
        {
          provide: UserRepository,
          useValue: mockUserRepository,
        },
      ],
    }).compile();

    service = module.get(UsersService);
    mockUserRepository = module.get(UserRepository);
    mockLogger = module.get(LoggerService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
  describe('findAllUsers', () => {
    it('should return users and count', async () => {
      const filter: UserFilterInterface = {
        pagination: { page: 1, limit: 10, sortOrder: SearchDirection.ASC, sortField: 'id' },
      };
      const mockResult = [[{ id: 1 }], 1];
      mockUserRepository.findUsersByFilter.mockResolvedValue(mockResult);
      const result = await service.findAllUsers(filter);
      expect(result).toBe(mockResult);
      expect(mockLogger.info).toHaveBeenCalled();
    });
  });

  describe('findUserById', () => {
    it('should return user when found', async () => {
      mockUserRepository.findUsersByFilter.mockResolvedValue([[{ id: 1 }], 1]);
      const result = await service.findUserById(1);
      expect(result).toEqual({ id: 1 });
    });
    it('should throw NotFoundException when not found', async () => {
      mockUserRepository.findUsersByFilter.mockResolvedValue([[], 0]);
      await expect(service.findUserById(1)).rejects.toThrow(NotFoundException);
    });
  });

  describe('findUserByEmail', () => {
    it('should return user when found', async () => {
      mockUserRepository.findUsersByFilter.mockResolvedValue([[{ id: 2, email: '<EMAIL>' }], 1]);
      const result = await service.findUserByEmail('<EMAIL>');
      expect(result).toEqual({ id: 2, email: '<EMAIL>' });
    });
    it('should throw NotFoundException when not found', async () => {
      mockUserRepository.findUsersByFilter.mockResolvedValue([[], 0]);
      await expect(service.findUserByEmail('<EMAIL>')).rejects.toThrow(NotFoundException);
    });
  });

  describe('createUser', () => {
    it('should create user and log', async () => {
      const userDetails: UserEntityInterface = {
        id: 1,
        username: 'john',
        email: '<EMAIL>',
        password: 'plain',
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      const createdUser = { ...userDetails, id: 2 };
      mockUserRepository.saveUser.mockResolvedValue(createdUser);
      const result = await service.createUser(userDetails);
      expect(result).toBe(createdUser);
      expect(mockLogger.info).toHaveBeenCalled();
    });
  });

  describe('updateUser', () => {
    it('should update user and log', async () => {
      const userDetails: UserEntityInterface = {
        id: 1,
        username: 'john',
        email: '<EMAIL>',
        password: 'plain',
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      const userInfo = { ...userDetails };
      const updatedUser = { ...userInfo, username: 'new', email: '<EMAIL>', password: 'newpass' };
      mockUserRepository.findUsersByFilter.mockResolvedValue([[userInfo], 1]);
      mockUserRepository.saveUser.mockResolvedValue(updatedUser);
      const result = await service.updateUser(
        {
          ...userDetails,
          username: 'new',
          email: '<EMAIL>',
          password: 'newpass',
        },
        userDetails.id,
      );
      expect(result).toBe(updatedUser);
      expect(mockLogger.info).toHaveBeenCalled();
    });
    it('should throw NotFoundException if user not found', async () => {
      mockUserRepository.findUsersByFilter.mockResolvedValue([[], 0]);
      await expect(service.updateUser(mockUser, mockUser.id)).rejects.toThrow(NotFoundException);
    });
  });

  describe('deleteUser', () => {
    it('should delete user and log', async () => {
      const userDetails: UserEntityInterface = {
        id: 1,
        username: 'john',
        email: '<EMAIL>',
        password: 'plain',
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      const userInfo = { ...userDetails };

      mockUserRepository.findUsersByFilter.mockResolvedValue([[userInfo], 1]);
      mockUserRepository.deleteUser.mockResolvedValue(userDetails);

      const result = await service.deleteUser(userDetails.id);

      expect(result).toBe(userDetails);
      expect(mockLogger.info).toHaveBeenCalled();
    });

    it('should throw NotFoundException if user not found', async () => {
      mockUserRepository.findUsersByFilter.mockResolvedValue([[], 0]);
      await expect(service.deleteUser(1)).rejects.toThrow(NotFoundException);
    });
  });
});
