import { Test, TestingModule } from '@nestjs/testing';
import { CommonController } from '../src/common/common.controller';
import { CommonService } from 'src/common/common.service';
import { Request, Response } from 'express';
import { HttpStatus } from '@nestjs/common';
import { Logger } from 'winston';
import { LoggerService } from '@services';
import { LogLevel } from '@enums';

describe('CommonController', () => {
  let commonController: CommonController;
  let commonService: CommonService;
  let mockLogger: jest.Mocked<LoggerService>;

  beforeEach(async () => {
    const loggerMock = {
      logger: {} as Logger,
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
      debug: jest.fn(),
      verbose: jest.fn(),
      log: jest.fn(),
    } as Partial<LoggerService> as jest.Mocked<LoggerService>;

    const module: TestingModule = await Test.createTestingModule({
      controllers: [CommonController],
      providers: [
        CommonService,
        {
          provide: LoggerService,
          useValue: loggerMock,
        },
      ],
    }).compile();

    commonController = module.get<CommonController>(CommonController);
    commonService = module.get<CommonService>(CommonService);
    mockLogger = module.get(LoggerService);
  });

  it('should be defined', () => {
    expect(commonController).toBeDefined();
  });

  it('should return health check response successfully and call loggerService', () => {
    const mockReq = {} as Request;

    const mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    } as unknown as Response;

    const commonServiceSpy = jest.spyOn(commonService, 'greetHello');

    commonController.health(mockReq, mockRes);

    expect(commonServiceSpy).toHaveBeenCalled();
    expect(mockLogger.log).toHaveBeenCalledWith(LogLevel.debug, 'Inside greetHello method.');
    expect(mockRes.status).toHaveBeenCalledWith(HttpStatus.OK);
    expect(mockRes.json).toHaveBeenCalledWith(
      expect.objectContaining({
        success: true,
        message: 'Hello',
        data: expect.objectContaining({
          uptime: expect.any(Number),
          message: 'Ok',
          date: expect.any(Date),
        }),
      }),
    );
  });
});
