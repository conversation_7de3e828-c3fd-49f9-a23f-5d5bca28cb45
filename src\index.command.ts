import { createMigration, generateMigration, help, migrationCheck, swagger } from '@commands';
import type { CommandsList } from '@interfaces';
import dotenv from 'dotenv';
import { LoggerService } from './services/logger.service';

dotenv.config();

// Mapping of commands to functions
const commandsList: CommandsList = {
  'migration:create': [createMigration, 'Create an empty migration file. Provide a name to describe the changes.'],
  'migration:generate': [generateMigration, 'Generate a migration file by analyzing changes in your models.'],
  'migration:check': [migrationCheck, 'Check if your migrations are up-to-date with the latest model changes.'],
  swagger: [swagger, 'Merge swagger part files into one.'],
};

// Get command-line argument
const [, , command] = process.argv;
// Extract CLI arguments
const args = process.argv.slice(2); // Ignore first two default args

const start = async (): Promise<void> => {
  const logger = new LoggerService();
  try {
    if (command === 'help') {
      help(commandsList);
    } else if (Object.keys(commandsList).includes(command)) {
      await commandsList[command][0](args);
      logger.debug(`Command : ${command} succussfully completed.`);
    } else {
      logger.error(`Invalid or missing seed command. Available commands: ${Object.keys(commandsList).join(', ')}`);
    }
  } catch (error) {
    // Log any errors and exit the process
    logger.error(error);
    process.exit(1);
  }
};

void start();
