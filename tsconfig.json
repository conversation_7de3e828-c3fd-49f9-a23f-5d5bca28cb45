{"files": ["src/main.ts"], "include": ["test"], "compilerOptions": {"lib": ["es2018", "dom"], "target": "es2018", "module": "commonjs", "baseUrl": "./", "outDir": "./dist", "paths": {"@commands": ["src/commands"], "@configs": ["src/configs"], "@enums": ["src/enums"], "@interfaces": ["src/interfaces"], "@middlewares": ["src/middlewares"], "@models": ["src/models"], "@plugins": ["src/plugins"], "@repository": ["src/repositories"], "@services": ["src/services"], "@swagger": ["src/swagger.swagger.yaml", "src/swagger"], "@tests": ["test/tests"], "@validators": ["src/validators"], "src/*": ["src/*"]}, "removeComments": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "strictPropertyInitialization": false, "resolveJsonModule": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictBindCallApply": true, "useUnknownInCatchVariables": true, "alwaysStrict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "skipLibCheck": true, "incremental": true}}