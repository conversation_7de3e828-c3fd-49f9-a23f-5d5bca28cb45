// import { LoggerService } from './services/logger.service';

import type { CommandsList } from '@interfaces';

export const help = (commandsList: CommandsList): void => {
  const commandsInfo: { command: string; description: string }[] = [
    { command: 'help', description: 'List all available commands.' },
  ];
  Object.keys(commandsList).forEach((commandName: string) => {
    const currentCommand = {
      command: commandName,
      description: commandsList[commandName][1],
    };
    commandsInfo.push(currentCommand);
  });

  console.table(commandsInfo);
};
