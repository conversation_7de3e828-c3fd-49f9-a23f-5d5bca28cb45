import { LogLevel } from '@enums';
import { Test, TestingModule } from '@nestjs/testing';
import { LoggerService } from '@services';
import { CommonService } from 'src/common/common.service';
import type { Logger } from 'winston';

describe('CommonService', () => {
  let service: CommonService;
  let mockLogger: jest.Mocked<LoggerService>;

  beforeEach(async () => {
    const loggerMock = {
      logger: {} as Logger,
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
      debug: jest.fn(),
      verbose: jest.fn(),
      log: jest.fn(),
    } as Partial<LoggerService> as jest.Mocked<LoggerService>;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CommonService,
        {
          provide: LoggerService,
          useValue: loggerMock,
        },
      ],
    }).compile();

    service = module.get<CommonService>(CommonService);
    mockLogger = module.get(LoggerService);
  });

  describe('greetHello', () => {
    it('should return "Hello" and log info message', () => {
      const result = service.greetHello();
      expect(mockLogger.log).toHaveBeenCalledWith(LogLevel.debug, 'Inside greetHello method.');
      expect(result).toBe('Hello');
    });

    it('should handle logger errors gracefully', () => {
      mockLogger.info.mockImplementation(() => {
        throw new Error('Logger error');
      });
      expect(() => {
        service.greetHello();
      }).not.toThrow('Logger error');
    });
  });
});
