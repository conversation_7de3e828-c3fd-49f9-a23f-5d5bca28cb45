import { User } from '@models';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CommonModule } from './common/common.module';
import { dataSourceOptions } from './configs/database.config';
import { UsersModule } from './users/users.module';

@Module({
  imports: [
    TypeOrmModule.forRoot({
      ...dataSourceOptions,
      entities: [User],
      retryAttempts: 3,
      retryDelay: 3000,
      autoLoadEntities: true,
    }),
    UsersModule,
    CommonModule,
  ],
})
export class AppModule {}
