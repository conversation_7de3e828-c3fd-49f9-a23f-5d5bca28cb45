import { Module } from '@nestjs/common';
import { dataSourceOptions } from './configs/database.config';
import { CommonModule } from './common/common.module';
import { UsersModule } from './users/users.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '@models';

@Module({
  imports: [
    TypeOrmModule.forRoot({
      ...dataSourceOptions,
      entities: [User],
    }),
    UsersModule,
    CommonModule,
  ],
})
export class AppModule {}
