import { <PERSON>, Get, HttpStatus, Req, Res } from '@nestjs/common';
import { CommonService } from './common.service';
import { CommonResponseInterface } from '@interfaces';
import { Request, Response } from 'express';

@Controller('common')
export class CommonController {
  constructor(private readonly commonService: CommonService) {}

  @Get()
  health(@Req() _req: Request, @Res() res: Response): void {
    try {
      const response: CommonResponseInterface = {
        success: true,
        message: this.commonService.greetHello(),
        data: {
          uptime: Math.floor(process.uptime()),
          message: 'Ok',
          date: new Date(),
        },
      };

      res.status(HttpStatus.OK).json(response);
    } catch (error: unknown) {
      console.log(error);
      throw error;
    }
  }
}
