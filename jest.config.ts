import type { Config } from 'jest';
import { pathsToModuleNameMapper } from 'ts-jest';
import { compilerOptions } from './tsconfig.json';

const config: Config = {
  preset: 'ts-jest',
  // This visually tells which project a test belongs to
  displayName: 'node-typescript-boilerplate',

  // test environment that will be used for testing
  testEnvironment: 'node',

  // Directories that <PERSON><PERSON> should ignore when looking for test files
  testPathIgnorePatterns: ['/node_modules/', '/vs-code-profile/'],

  // Module file extensions for importing
  moduleFileExtensions: ['js', 'ts', 'json'],

  // Pattern for test files
  testRegex: '.*\\.spec\\.ts$',

  // This maps your aliases for Jest.
  // It uses regex to match the alias and replace it with the actual path.
  // Make sure this matches your tsconfig.json paths exactly.
  moduleNameMapper: pathsToModuleNameMapper(compilerOptions.paths, { prefix: '<rootDir>' }),
  modulePaths: ['<rootDir>'],
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },

  collectCoverageFrom: ['**/*.(t|j)s'],

  // Coverage report output directory
  coverageDirectory: 'test_coverage',

  // Reporters to use for coverage
  coverageReporters: ['json', 'lcov', 'text', 'clover'],
  // lcov (Line Coverage)
  // This is a text-based format that specifies which lines of code are covered by tests.
  // clover
  // This is an XML-based format. It's derived from the Clover XML report format.

  // Setup files to run before tests in the environment
  // This is useful for setting up global variables, mocking databases, etc.
  setupFilesAfterEnv: ['./src/configs/common.config.ts'],

  // minimum threshold enforcement for coverage results
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
};

export default config;
