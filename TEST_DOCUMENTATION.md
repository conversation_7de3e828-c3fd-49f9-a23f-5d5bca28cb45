# Test Documentation

## Overview

This document provides comprehensive information about the test suite for the Node.js TypeScript Boilerplate, focusing on the User and Math modules.

## Test Structure

```
src/tests/
├── math.test.ts          # Math controller tests
├── users.test.ts         # User module tests
└── run-tests.sh          # Test runner script
```

## Test Coverage

### 1. User Module Tests (`users.test.ts`)

#### **Controller Tests**

- ✅ `fetchUsers` - Fetch users with default pagination
- ✅ `fetchUsers` - Fetch users with custom pagination parameters
- ✅ `fetchUsers` - Handle errors and call next middleware
- ✅ `createUser` - Create user successfully
- ✅ `createUser` - Handle errors during user creation
- ✅ `updateUser` - Update user successfully
- ✅ `updateUser` - Handle errors during user update

#### **Service Tests**

- ✅ `findAllUsers` - Return users and count
- ✅ `findUserById` - Return user when found
- ✅ `findUserById` - Throw NotFoundException when user not found
- ✅ `findUserByEmail` - Return user when found by email
- ✅ `findUserByEmail` - Throw NotFoundException when user not found by email
- ✅ `createUser` - Create user successfully
- ✅ `updateUser` - Update user successfully

#### **Repository Tests**

- ✅ `findUsersByFilter` - Build correct query options for basic filter
- ✅ `findUsersByFilter` - Build correct query options with search key
- ✅ `saveUser` - Save user with hashed password

### 2. Math Module Tests (`math.test.ts`)

- ✅ Basic arithmetic operations
- ✅ Generic math function with different operations

## Running Tests

### Using NPM Scripts

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch
```

### Using Test Runner Script

```bash
# Make script executable (first time only)
chmod +x src/tests/run-tests.sh

# Run all tests
./src/tests/run-tests.sh

# Run tests with coverage
./src/tests/run-tests.sh coverage

# Run tests in watch mode
./src/tests/run-tests.sh watch

# Run specific module tests
./src/tests/run-tests.sh users
./src/tests/run-tests.sh math
```

### Using Jest Directly

```bash
# Run specific test file
npx jest src/tests/users.test.ts

# Run tests with coverage for specific file
npx jest src/tests/users.test.ts --coverage

# Run tests matching pattern
npx jest --testNamePattern="createUser"

# Run tests in verbose mode
npx jest --verbose
```

## Test Configuration

### Jest Configuration (`jest.config.ts`)

```typescript
{
  preset: 'ts-jest',
  testEnvironment: 'node',
  testMatch: ['**/tests/**/*.ts', '**/?(*.)+(spec|test).ts'],
  moduleNameMapper: {
    '^@controllers/(.*)$': '<rootDir>/src/controllers/$1.controller',
  },
  collectCoverage: true,
  coverageDirectory: 'test_coverage',
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
}
```

### Coverage Requirements

- **Branches**: 80%
- **Functions**: 80%
- **Lines**: 80%
- **Statements**: 80%

## Mocking Strategy

### 1. Service Layer Mocking

```typescript
jest.mock('@services/users');

const MockedUserService = UserService as jest.MockedClass<typeof UserService>;
```

### 2. Repository Layer Mocking

```typescript
jest.mock('@repository/user');

const MockedUserRepository = UserRepository as jest.MockedClass<typeof UserRepository>;
```

### 3. Model Mocking

```typescript
jest.spyOn(require('@models/user'), 'User').mockImplementation(() => ({
  findAndCount: mockFindAndCount,
  save: mockSave,
}));
```

## Test Data

### Mock User Data

```typescript
const mockUser = {
  id: 1,
  username: 'testuser',
  email: '<EMAIL>',
  password: 'hashedpassword',
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
} as User;
```

## Test Patterns

### 1. Controller Test Pattern

```typescript
describe('Controller Method', () => {
  it('should handle successful operation', async () => {
    // Arrange
    const mockRequest = {
      /* request data */
    } as any;
    const mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    } as any;
    const mockNext = jest.fn();

    // Mock service method
    MockedService.prototype.method = jest.fn().mockResolvedValue(result);

    // Act
    await controller.method(mockRequest, mockResponse, mockNext);

    // Assert
    expect(mockResponse.status).toHaveBeenCalledWith(StatusCodes.OK);
    expect(mockResponse.json).toHaveBeenCalledWith(expectedResponse);
  });

  it('should handle errors', async () => {
    // Arrange
    const mockError = new Error('Test error');
    MockedService.prototype.method = jest.fn().mockRejectedValue(mockError);

    // Act
    await controller.method(mockRequest, mockResponse, mockNext);

    // Assert
    expect(mockNext).toHaveBeenCalledWith(mockError);
  });
});
```

### 2. Service Test Pattern

```typescript
describe('Service Method', () => {
  it('should perform operation successfully', async () => {
    // Arrange
    const input = {
      /* input data */
    };
    const expectedOutput = {
      /* expected result */
    };

    MockedRepository.prototype.method = jest.fn().mockResolvedValue(expectedOutput);

    // Act
    const result = await service.method(input);

    // Assert
    expect(result).toEqual(expectedOutput);
    expect(MockedRepository.prototype.method).toHaveBeenCalledWith(input);
  });
});
```

### 3. Repository Test Pattern

```typescript
describe('Repository Method', () => {
  it('should build correct query options', async () => {
    // Arrange
    const filter = {
      /* filter data */
    };
    const mockMethod = jest.fn().mockResolvedValue([[], 0]);

    jest.spyOn(require('@models/entity'), 'Entity').mockImplementation(() => ({
      findAndCount: mockMethod,
    }));

    // Act
    await repository.method(filter);

    // Assert
    expect(mockMethod).toHaveBeenCalledWith(expectedQueryOptions);
  });
});
```

## Best Practices

### 1. Test Organization

- Group related tests using `describe` blocks
- Use descriptive test names that explain the scenario
- Follow the Arrange-Act-Assert pattern

### 2. Mocking

- Mock external dependencies (services, repositories)
- Use `jest.clearAllMocks()` in `beforeEach` to reset mocks
- Mock at the appropriate level (service for controller tests, repository for service tests)

### 3. Error Testing

- Always test error scenarios
- Verify that errors are properly propagated
- Test both expected and unexpected errors

### 4. Edge Cases

- Test with empty data
- Test with invalid data
- Test boundary conditions (pagination limits, etc.)

### 5. Integration Testing

- Test complete workflows
- Test data flow between layers
- Test real-world scenarios

## Continuous Integration

### GitHub Actions Example

```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '18'
      - run: npm ci
      - run: npm test
      - run: npm run test:coverage
```

## Troubleshooting

### Common Issues

1. **Import Path Errors**

   - Ensure Jest moduleNameMapper matches tsconfig paths
   - Check that all imports use the correct alias format

2. **Mock Not Working**

   - Verify mock is placed before imports
   - Check that mock path matches actual import path
   - Use `jest.clearAllMocks()` in beforeEach

3. **Type Errors**

   - Ensure test files are included in tsconfig
   - Use proper type assertions for mocks
   - Check that mock types match actual types

4. **Coverage Issues**
   - Verify that all code paths are tested
   - Check for conditional logic that's not covered
   - Ensure error scenarios are tested

### Debug Commands

```bash
# Run tests with verbose output
npx jest --verbose

# Run specific test with debug info
npx jest --verbose --testNamePattern="specific test name"

# Check Jest configuration
npx jest --showConfig

# Run tests with coverage and open report
npx jest --coverage --coverageReporters=html
```

## Future Enhancements

1. **E2E Tests**

   - Add end-to-end tests using Supertest
   - Test complete API workflows
   - Test database integration

2. **Performance Tests**

   - Add load testing with Artillery
   - Test database query performance
   - Monitor memory usage

3. **Security Tests**

   - Add security vulnerability tests
   - Test input validation thoroughly
   - Test authentication/authorization

4. **Database Tests**
   - Add integration tests with test database
   - Test migrations
   - Test data integrity

This comprehensive test suite ensures the reliability and maintainability of the Node.js TypeScript Boilerplate codebase.
