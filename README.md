# 🚀 Node Typescript Boilerplate

> A modern Node.js project written in TypeScript and NestJS. This will be used as a base framework for the microservices of RDT.

## 📦 Features

- ⚡ VS Code Profile Setup
- 🧪 Jest for testing
- 🛠️ ESLint + Prettier
- 🔧 Configurable via `.env`
- 🚀 Fast builds with TypeScript `tsc`

## VS Code Profile Setup ⚡

1. Make sure you have installed VS Code latest version.
2. Import given profile `vs-code-profile/rdt-node-express-ts.code-profile` into your VS Code. [Reference Import VS Code Profile](https://code.visualstudio.com/docs/configure/profiles#_import)
3. Open this project in VS Code with above profile. Windows users can run bash file `vs-code-profile/setup-vscode-windows.bat` to open.
4. Validate Active Profile at left bottom. ![Validate Active Profile](vs-code-profile/vs-code-active-profile.png)

- This VS Code profile will have the necessary settings to be in sync with code formatting and install the plugins which will be useful for the project.

## Installation 📦

1. **Clone the repository** 📁

   ```sh
   > git clone <repository-url>
   > cd <repository-directory>
   ```

2. **Install Dependencies** ⬇️

   ```
   > npm install
   ```

3. **Configuration** ⚙️

   1. Create a copy of `.env.sample` file in the root directory of the project.
   2. Rename above file as `.env`
   3. Default values are already mentioned change them asper the project enviournment need.
   4. On keeping empty values it will use mentioned default values.

4. **NPM Scripts** 🚀

   - `npm run build` - Generate the project build
   - `npm run start` - Starts the server in production mode
   - `npm run start:dev` - Starts the server in development mode
   - `npm run start:prod` - Starts the server in production mode
   - `npm run lint` - Highlight the linting errors
   - `npm run lint:fix` - Fix all auto fixable linting errors
   - `npm run prettier` - Highlight the formatting errors
   - `npm run prettier:fix` - Fix all auto fixable formatting errors
   - `npm run test` - Execute all test cases
   - `npm run test:watch` - Run tests in watch mode
   - `npm run test:cov` - Run tests with coverage
   - `npm run commit` or `git-cz` or `git cz` - Husky commit (Run after git add command)
   - `npm run typeorm` - Perform typeorm related commands
   - `npm run migration:run` - Run pending migrations on database
   - `npm run migration:revert` - Revert last migration on database
   - `npm run commands` - Execute custom created commands

## Folder Structure 📁

```
node-typescript-boilerplate/
├── .husky/
│   ├── pre-commit         # git commit pre-check configs
├── src/
│   ├── commands/          # Background tasks
│   ├── config/            # Global config variables
│   ├── controllers/       # Route controllers
│   ├── enums/             # Global Constants
│   ├── exceptions/        # Custom exceptions
│   ├── interfaces/        # Custom Type interfaces
│   ├── middlewares/       # Express middlewares
│   ├── migrations/        # Database Migrations
│   ├── models/            # Database Entity Models
│   ├── plugins/           # Enhanced features to be attached into express
│   ├── repositories/      # Repository for DB operations
│   ├── routes/            # API route definitions
│   ├── services/          # Business logic
│   ├── swagger/           # API Swagger Documentation
│   ├── validators/        # API input validation schema
│   └── index.command.ts   # Server startup file for command running
│   └── main.ts           # Server startup file
├── vs-code-profile/
│   └── rdt-node-express-ts.code-profile     # VS Code profile
├── .commitlintrc.json     # git commit precheck configuration
├── .env.sample            # Environment variables configuration
├── .gitignore
├── .prettierignore
├── .prettierrc            # Prettier(formatting) config
├── bitbucket-pipelines.yml     # Bitbucket CI/CD pipeline
├── eslint.config.js       # Linting rules
├── nest-cli.json          # NestJS CLI configuration
├── nodemon.json           # dev server run params
├── package.json           # NPM scripts and dependencies
├── tsconfig.json          # TypeScript configuration
└── README.md              # Project documentation
```

## Custom Commands Execution 🛠️

1. `npm run commands -- help` Show the list of existing commands
2. `npm run commands -- migration:create "MyMigrationName"` Create empty migration file with provided name
3. `npm run commands -- migration:generate  "MyMigrationName"` Generate migration file with provided name from created or modified models.
4. `npm run commands -- migration:check` Provide status if any migration pending to execute on connected database or not.

## API Documentation - Swagger 📚

1. Access Swagger UI - Once the application is running, open your browser and navigate to:
   ```
   http://localhost:3000/documentation
   ```
2. This URL is based on the SWAGGER_URL variable defined in your .env file.
3. For API documentation, create new `<entity-name>.swagger.yaml` files into `/src/swagger/` folder.
4. Use command `npm run commands -- swagger` to generate combined Open API file ready (`src/swagger/swagger.yaml`) to be imported in other third parties like Postman, AWS API Gateway etc.
