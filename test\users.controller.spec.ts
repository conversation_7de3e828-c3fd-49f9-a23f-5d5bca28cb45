import { Test, TestingModule } from '@nestjs/testing';
import { UsersController } from '../src/users/users.controller';
import { User } from '../src/models/user.model';
import { Request, Response } from 'express';
import { UsersService } from '../src/users/users.service';
import { HttpStatus } from '@nestjs/common';
import { LoggerService } from '@services';
import { CreateUserDTO, ListPaginationQuery, UpdateUserDTO } from '@validators';

describe('UsersController', () => {
  let usersController: UsersController;
  let usersService: UsersService;
  const mockUsersService = {
    findAllUsers: jest.fn(),
    createUser: jest.fn(),
    updateUser: jest.fn(),
    findUserById: jest.fn(),
    deleteUser: jest.fn(),
  };

  const mockUser = {
    id: 1,
    username: 'testuser',
    email: '<EMAIL>',
    password: 'hashedpassword',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  } as unknown as User;

  const mockQuery: ListPaginationQuery = {
    limit: 10,
    page: 1,
    sortOrder: 'ASC',
    sortField: '',
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UsersController],
      providers: [
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
        {
          provide: LoggerService,
          useValue: {
            info: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
          },
        },
      ],
    }).compile();

    usersController = module.get<UsersController>(UsersController);
    usersService = module.get<UsersService>(UsersService);
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(usersController).toBeDefined();
  });
  describe('fetchUsers', () => {
    it('should fetch users successfully with default pagination', async () => {
      const mockRequest = { query: {} } as unknown as Request;
      const mockResponse = { status: jest.fn().mockReturnThis(), json: jest.fn() } as unknown as Response;
      const mockUsers = [mockUser];
      const mockTotalUsers = 1;
      mockUsersService.findAllUsers.mockResolvedValue([mockUsers, mockTotalUsers]);

      await usersController.fetchUsers(mockRequest, mockResponse, mockQuery);

      expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Users fetched successfully.',
        data: {
          totalUsers: mockTotalUsers,
          users: mockUsers,
          metadata: { page: 1, limit: 10 },
        },
      });
    });

    it('should fetch users with custom pagination parameters', async () => {
      const mockRequest = { query: {} } as unknown as Request;
      const mockQuery: ListPaginationQuery = {
        page: 2,
        limit: 5,
        sortOrder: 'DESC',
        sortField: 'createdAt',
      };
      const mockResponse = { status: jest.fn().mockReturnThis(), json: jest.fn() } as unknown as Response;
      const mockUsers = [mockUser];
      const mockTotalUsers = 1;

      mockUsersService.findAllUsers.mockResolvedValue([mockUsers, mockTotalUsers]);

      await usersController.fetchUsers(mockRequest, mockResponse, mockQuery);

      expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Users fetched successfully.',
        data: {
          totalUsers: mockTotalUsers,
          users: mockUsers,
          metadata: { page: 2, limit: 5 },
        },
      });
    });

    it('should handle errors', async () => {
      const mockRequest = { query: {} } as unknown as Request;
      const mockResponse = { status: jest.fn().mockReturnThis(), json: jest.fn() } as unknown as Response;
      const mockError = new Error('Database error');
      mockUsersService.findAllUsers.mockRejectedValue(mockError);

      await expect(usersController.fetchUsers(mockRequest, mockResponse, mockQuery)).rejects.toThrow('Database error');
    });

    it('should handle empty user list', async () => {
      const mockRequest = { query: {} } as unknown as Request;
      const mockResponse = { status: jest.fn().mockReturnThis(), json: jest.fn() } as unknown as Response;
      mockUsersService.findAllUsers.mockResolvedValue([[], 0]);

      await usersController.fetchUsers(mockRequest, mockResponse, mockQuery);

      expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Users fetched successfully.',
        data: {
          totalUsers: 0,
          users: [],
          metadata: { page: 1, limit: 10 },
        },
      });
    });

    it('should handle invalid pagination params gracefully', async () => {
      const mockRequest = { query: { page: 'notanumber', limit: 'notanumber' } } as unknown as Request;
      const mockResponse = { status: jest.fn().mockReturnThis(), json: jest.fn() } as unknown as Response;
      const mockUsers = [mockUser];
      const mockTotalUsers = 1;
      mockUsersService.findAllUsers.mockResolvedValue([mockUsers, mockTotalUsers]);

      await usersController.fetchUsers(mockRequest, mockResponse, mockQuery);

      expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Users fetched successfully.',
        data: {
          totalUsers: mockTotalUsers,
          users: mockUsers,
          metadata: { page: 1, limit: 10 },
        },
      });
    });
  });

  describe('createUser', () => {
    it('should create user successfully', async () => {
      const mockRequest = { body: mockUser } as unknown as Request;
      const mockResponse = { status: jest.fn().mockReturnThis(), json: jest.fn() } as unknown as Response;
      mockUsersService.createUser.mockResolvedValue(mockUser);

      await usersController.createUser(mockRequest, mockResponse, mockUser);

      expect(usersService.createUser).toHaveBeenCalledWith(mockUser);
      expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'User created successfully.',
        data: { user: mockUser },
      });
    });

    it('should handle errors during user creation', async () => {
      const mockRequest = { body: mockUser } as unknown as Request;
      const mockResponse = { status: jest.fn().mockReturnThis(), json: jest.fn() } as unknown as Response;
      const mockError = new Error('Bad Request Error');
      mockUsersService.createUser.mockRejectedValue(mockError);

      await expect(usersController.createUser(mockRequest, mockResponse, mockUser)).rejects.toThrow(
        'Bad Request Error',
      );
    });

    it('should handle missing body', async () => {
      const mockRequest = { body: undefined } as unknown as Request;
      const mockResponse = { status: jest.fn().mockReturnThis(), json: jest.fn() } as unknown as Response;
      mockUsersService.createUser.mockResolvedValue(mockUser);

      await usersController.createUser(mockRequest, mockResponse, undefined as unknown as CreateUserDTO);
      // Should still call service with undefined, but service should handle validation

      expect(usersService.createUser).toHaveBeenCalledWith(undefined);
    });
  });

  describe('updateUser', () => {
    it('should update user successfully', async () => {
      const mockId = 1;
      mockUsersService.updateUser.mockResolvedValue(mockUser);

      const result = await usersController.updateUser(mockUser, { id: mockId });

      expect(mockUsersService.updateUser).toHaveBeenCalledWith(mockUser, mockId);
      expect(result).toEqual({
        success: true,
        message: 'User updated successfully.',
        data: {
          user: mockUser,
        },
      });
    });

    it('should throw an error if user not found', async () => {
      const mockId = 1;
      const mockError = new Error('User not found');
      mockUsersService.updateUser.mockRejectedValue(mockError);

      await expect(usersController.updateUser(mockUser, { id: mockId })).rejects.toThrow('User not found');
    });

    it('should handle missing body', async () => {
      const mockId = 1;
      mockUsersService.updateUser.mockResolvedValue(mockUser);

      await usersController.updateUser(undefined as unknown as UpdateUserDTO, { id: mockId });

      expect(usersService.updateUser).toHaveBeenCalledWith(undefined, mockId);
    });
  });

  describe('findUserById', () => {
    it('should find user by id successfully', async () => {
      const mockId = 1;
      const mockUsers = [mockUser];
      const mockTotalUsers = 1;
      mockUsersService.findAllUsers.mockResolvedValue([mockUsers, mockTotalUsers]);

      const result = await usersController.getUser({ id: mockId }, mockQuery);

      expect(mockUsersService.findAllUsers).toHaveBeenCalled();
      expect(result).toEqual({
        success: true,
        message: 'Users fetched successfully.',
        data: {
          totalUsers: mockTotalUsers,
          users: mockUsers,
          metadata: { page: 1, limit: 10 },
        },
      });
    });

    it('should throw an error if user not found', async () => {
      const mockId = 1;
      const mockError = new Error('User not found');
      mockUsersService.findAllUsers.mockRejectedValue(mockError);

      await expect(usersController.getUser({ id: mockId }, mockQuery)).rejects.toThrow('User not found');
    });
  });

  describe('deleteUser', () => {
    it('should delete user successfully', async () => {
      const mockId = 1;
      mockUsersService.deleteUser.mockResolvedValue(mockUser);

      const result = await usersController.deleteUser({ id: mockId });

      expect(mockUsersService.deleteUser).toHaveBeenCalledWith(mockId);
      expect(result).toEqual({
        success: true,
        message: 'User deleted successfully.',
        data: {
          user: mockUser,
        },
      });
    });

    it('should throw an error if user not found', async () => {
      const mockId = 1;
      const mockError = new Error('User not found');
      mockUsersService.deleteUser.mockRejectedValue(mockError);

      await expect(usersController.deleteUser({ id: mockId })).rejects.toThrow('User not found');
    });
  });
});
