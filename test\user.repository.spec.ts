import { SearchDirection } from '@enums';
import type { UserEntityInterface, UserFilterInterface } from '@interfaces';
import { User } from '@models';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { UserRepository } from '@repository';
import bcrypt from 'bcryptjs';
import { Repository } from 'typeorm';

describe('UserRepository', () => {
  let userRepository: UserRepository;
  let mockUserRepo: Partial<Repository<User>>;

  beforeEach(async () => {
    mockUserRepo = {
      findAndCount: jest.fn(),
      save: jest.fn(),
      delete: jest.fn(),
    };
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserRepository,
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepo,
        },
      ],
    }).compile();

    userRepository = module.get<UserRepository>(UserRepository);
    jest.clearAllMocks();
  });

  describe('findUsersByFilter', () => {
    it('should call User.findAndCount with correct options (no searchKey)', async () => {
      const filter: UserFilterInterface = {
        id: 1,
        username: 'mockuser1',
        email: '<EMAIL>',
        createdAt: undefined,
        updatedAt: undefined,
        pagination: {
          page: 1,
          limit: 10,
          sortOrder: SearchDirection.ASC,
          sortField: 'id',
        },
      };

      const mockUser = new User();
      mockUser.id = 1;
      mockUser.username = 'mockuser1';
      mockUser.email = '<EMAIL>';
      mockUser.password = 'mockpass';
      mockUser.createdAt = new Date();
      mockUser.updatedAt = new Date();

      const mockResult: [User[], number] = [[mockUser], 1];
      const findAndCountSpy = jest.spyOn(mockUserRepo, 'findAndCount').mockResolvedValue(mockResult);

      const result = await userRepository.findUsersByFilter(filter);

      expect(findAndCountSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          skip: 0,
          take: 10,
          where: expect.objectContaining({
            id: 1,
            username: expect.objectContaining({
              value: expect.stringContaining('mockuser1'),
            }),
            email: expect.objectContaining({
              value: expect.stringContaining('<EMAIL>'),
            }),
          }),
          order: { id: 'ASC' },
        }),
      );

      expect(result).toBe(mockResult);
    });

    it('should call User.findAndCount with correct options (with searchKey)', async () => {
      const filter: UserFilterInterface = {
        searchKey: 'john',
        id: undefined,
        username: undefined,
        email: undefined,
        createdAt: undefined,
        updatedAt: undefined,
        pagination: {
          page: 1,
          limit: 10,
          sortOrder: SearchDirection.ASC,
          sortField: 'id',
        },
      };

      const mockUser = new User();
      mockUser.id = 2;
      mockUser.username = 'mockuser2';
      mockUser.email = '<EMAIL>';
      mockUser.password = 'mockpass';
      mockUser.createdAt = new Date();
      mockUser.updatedAt = new Date();

      const mockResult: [User[], number] = [[mockUser], 1];
      const findAndCountSpy = jest.spyOn(mockUserRepo, 'findAndCount').mockResolvedValue(mockResult);

      const result = await userRepository.findUsersByFilter(filter);

      expect(findAndCountSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          skip: 0,
          take: 10,
          where: expect.any(Array) as unknown as object[],
          order: { id: 'ASC' },
        }),
      );

      expect(result).toBe(mockResult);
    });
  });

  describe('saveUser', () => {
    it('should hash password and call User.save', async () => {
      const now = new Date();
      const userDetails: UserEntityInterface = {
        id: 1,
        username: 'john',
        email: '<EMAIL>',
        password: 'plain',
        createdAt: now,
        updatedAt: now,
      };

      const hashed = 'hashed-password';
      jest.spyOn(bcrypt, 'hashSync').mockReturnValue(hashed);

      const savedUser = new User();
      savedUser.id = userDetails.id;
      savedUser.username = userDetails.username;
      savedUser.email = userDetails.email;
      savedUser.password = hashed;
      savedUser.createdAt = userDetails.createdAt!;
      savedUser.updatedAt = userDetails.updatedAt!;

      const saveSpy = jest.spyOn(mockUserRepo, 'save').mockResolvedValue(savedUser);

      const result = await userRepository.saveUser(userDetails);

      expect(bcrypt.hashSync).toHaveBeenCalledWith('plain', 8);
      expect(saveSpy).toHaveBeenCalledWith(savedUser);
      expect(result).toEqual(savedUser);
    });
  });

  describe('deleteUser', () => {
    it('should delete user successfully', async () => {
      const now = new Date();
      const userDetails: UserEntityInterface = {
        id: 1,
        username: 'john',
        email: '<EMAIL>',
        password: 'plain',
        createdAt: now,
        updatedAt: now,
      };
      const mockResponse = {
        raw: userDetails,
        affected: 1,
      };

      const deleteSpy = jest.spyOn(mockUserRepo, 'delete').mockResolvedValue(mockResponse);

      const result = await userRepository.deleteUser(userDetails.id);

      expect(deleteSpy).toHaveBeenCalledWith({ id: userDetails.id });
      expect(result).toEqual(mockResponse);
    });
  });
});
