#!/usr/bin/env sh
RED='\033[0;31m'
GREEN='\033[0;32m'
NO_COLOR='\033[0m'

if ! npx --no -- commitlint --edit "$1"; then
  printf "\n${RED}Commit message does not follow the required format.\n"
  printf "Please ensure your commit message adheres to the commitlint rules.\n${NO_COLOR}\n"
  printf  "Run command ${GREEN}'npm run commit'${NO_COLOR} to commit changes.\n\n"
  exit 1
else
  printf "\n${GREEN}Commit successful. You can push the changes now.${NO_COLOR}\n\n"
fi

exit 0