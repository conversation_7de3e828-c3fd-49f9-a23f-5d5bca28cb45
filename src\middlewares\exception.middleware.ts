// TODO: Consider splitting the middlewares into multiple files. We can have http exception middleware and database exception middleware for example.

import { CommonResponseInterface } from '@interfaces';
import {
  ArgumentsHost,
  BadRequestException,
  Catch,
  type ExceptionFilter,
  HttpException,
  HttpStatus,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { LoggerService } from '../services/logger.service';
import { Response } from 'express';
/**
 * Logs the exception and prepare the standard response for exception.
 *
 * @param err Error
 * @param req Request
 * @param res Response
 * @param next NextFunction
 */
@Catch()
export class ExceptionMiddleware implements ExceptionFilter {
  catch(err: Error, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const res = ctx.getResponse<Response>();
    // const request = ctx.getRequest<Request>();

    const logger = new LoggerService();
    let errorMessage: string = 'Internal Server Error';
    let statusCode: number = HttpStatus.INTERNAL_SERVER_ERROR;
    const errorStack = err.stack;

    if (err instanceof BadRequestException) {
      statusCode = err.getStatus(); // should be 400
      errorMessage = err.message;
      logger.error('Bad Request.', { errorMessage, errorStack });
    } else if (err instanceof NotFoundException) {
      statusCode = err.getStatus(); // 404
      errorMessage = err.message;
      logger.error('Not Found.', { errorMessage, errorStack });
    } else if (err instanceof UnauthorizedException) {
      statusCode = err.getStatus(); // 401
      errorMessage = err.message;
      logger.error('Unauthorized.', { errorMessage, errorStack });
    } else if (err instanceof HttpException) {
      // Fallback for any other HttpException types (e.g., ForbiddenException, ConflictException, etc.)
      statusCode = err.getStatus();
      errorMessage = err.message;
      logger.error('HTTP Exception.', { errorMessage, errorStack });
    } else {
      // Non-error objects thrown?
      statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
      errorMessage = err.message;
      logger.error('Unknown error type.', { errorMessage, errorStack });
    }

    const response: CommonResponseInterface = {
      success: false,
      message: errorMessage,
      data: null,
    };

    // throw new HttpException(response, statusCode);
    res.status(statusCode).json(response);
  }
}
