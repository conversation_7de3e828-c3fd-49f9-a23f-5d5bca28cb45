import { loggerConfig } from '@configs';
import { LogLevel } from '@enums';
import type { GenericObject, LogAttributes, LogEntry } from '@interfaces';
// import { requestIdStorage } from '@middlewares';
import { Global, Injectable } from '@nestjs/common';
// import type { DiagLogLevel } from '@opentelemetry/api';
// import { diag, DiagConsoleLogger } from '@opentelemetry/api';
import type { LoggerOptions } from 'winston';
import winston from 'winston';
@Global()
@Injectable()
export class LoggerService {
  logger: winston.Logger;

  constructor() {
    // const requestId = requestIdStorage.getStore(); // Get the requestId from ALS
    const loggerOptions: LoggerOptions = {
      level: LogLevel.silly,
      format: winston.format.combine(
        winston.format.timestamp({ format: loggerConfig.timestampFormat }),
        winston.format.json(),
        winston.format.colorize({
          all: true,
          colors: {
            error: 'red',
            warn: 'yellow',
            info: 'cyan',
            debug: 'green',
          },
        }),
        // winston.format.prettyPrint({ colorize: true, depth: LoggerConfig.logsReadableFormat }),
        winston.format.uncolorize({ message: true, level: true, raw: false }),
        // winston.format.printf(({ ...data }) =>
        //   this._coloriseMessage(JSON.stringify(data, null, LoggerConfig.logsReadableFormat)),
        // ),
      ),
      // defaultMeta: { service: this._serviceName, requestId },
      transports: [
        new winston.transports.Console(), // Output to console
        // You can add other transports like file, network, etc.
        // new winston.transports.File({ filename: 'logs.log', maxsize: 1 }),
      ],
    };

    this.logger = winston.createLogger(loggerOptions);
  }

  info(message: unknown, attributes?: LogAttributes): void {
    this._log(LogLevel.info, message, attributes);
  }

  warn(message: unknown, attributes?: LogAttributes): void {
    this._log(LogLevel.warn, message, attributes);
  }

  error(message: unknown, attributes?: LogAttributes): void {
    this._log(LogLevel.error, message, attributes);
  }

  debug(message: unknown, attributes?: LogAttributes): void {
    this._log(LogLevel.debug, message, attributes);
  }

  verbose(message: unknown, attributes?: LogAttributes): void {
    this._log(LogLevel.verbose, message, attributes);
  }

  log(logLevel: LogLevel, message: unknown, attributes?: LogAttributes): void {
    this._log(logLevel, message, attributes);
  }

  /**
   *
   * @param level string
   * @param body unknown
   * @param attributes LogAttributes
   */
  private _log(level: LogLevel, body: unknown, attributes?: LogAttributes): void {
    const logEntry: LogEntry = {
      timestamp: new Date().toISOString(),
      severity: level.toUpperCase(),
      body: this._replaceSensitiveValues(body as GenericObject),
      attributes,
    };
    this.logger.log(level, '', logEntry); // Winston's message is empty as all info is in meta

    // For troubleshooting, set the log level to DiagLogLevel.DEBUG
    // diag.setLogger(new DiagConsoleLogger(), LogLevelMapping[level] as unknown as DiagLogLevel);
  }

  /**
   * Replace sensitive attributes with provided new masked value.
   *
   * @param data unknown
   * @param newValue string
   * @returns
   */
  private readonly _replaceSensitiveValues = (
    data: GenericObject,
    newValue: string = loggerConfig.maskedValue,
  ): GenericObject => {
    if (Array.isArray(data)) {
      data.forEach((item, index) => {
        if (typeof item === 'object' && item !== null) {
          data[index] = this._replaceSensitiveValues(item as GenericObject, newValue);
        }
      });
      return data;
    } else if (typeof data === 'object') {
      const keys = Object.keys(data);
      keys.forEach((key: string) => {
        if (loggerConfig.maskProperties.includes(key)) {
          data[key] = newValue;
        } else if (typeof data[key] === 'object') {
          this._replaceSensitiveValues(data[key] as GenericObject, newValue);
        }
      });
      return data;
    }

    return data; // Handle primitive types
  };

  /**
   * Add appropriate color to Error, Warn, Info message text, in json.
   *
   * @param message string
   * @returns string
   */
  // private _coloriseMessage = (message: string): string =>
  //   message.replace(/\\u001b\[\d+m/g, (match) => match.replace(/\\u001b\[/g, '\u001b['));
}
