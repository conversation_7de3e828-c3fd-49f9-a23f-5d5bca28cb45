import { execSync } from 'child_process';
import { LoggerService } from '../services/logger.service';

export const createMigration = (_args: string[]): void => {
  const migrationName = _args[1];
  const logger = new LoggerService();

  if (!migrationName) {
    logger.error('Error: Please provide a migration name as an argument.');
    process.exit(1);
  }

  try {
    const command = `typeorm-ts-node-esm migration:create ./src/migrations/${migrationName}`;
    logger.info(`Creating migration: ${command}`);
    execSync(command, { stdio: 'inherit' });
  } catch (error) {
    logger.error('Error creating migration:');
    logger.error(error);
    process.exit(1);
  }
};
