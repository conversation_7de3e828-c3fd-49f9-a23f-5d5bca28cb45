import { LogLevel } from '@enums';
import winston from 'winston';
import { loggerConfig } from '../src/configs/logger.config';
import { LoggerService } from '@services';
import { Test, TestingModule } from '@nestjs/testing';

// Mock winston and its transports for strict ESLint/TS compliance
jest.mock('winston', () => {
  const actualWinston = jest.requireActual('winston');
  return {
    ...actualWinston,
    createLogger: jest.fn((): unknown => ({
      log: jest.fn(),
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
      debug: jest.fn(),
      verbose: jest.fn(),
    })),
    transports: {
      ['Console']: jest.fn(),
      ['File']: jest.fn().mockImplementation(() => ({})),
    },
    format: actualWinston.format,
  };
});

describe('LoggerService', () => {
  let loggerService: LoggerService;
  let mockWinstonLogger: { log: jest.Mock };

  beforeEach(async () => {
    jest.clearAllMocks();
    mockWinstonLogger = { log: jest.fn() };
    (winston.createLogger as jest.Mock).mockReturnValue(mockWinstonLogger);
    const module: TestingModule = await Test.createTestingModule({
      providers: [LoggerService],
    }).compile();

    loggerService = module.get<LoggerService>(LoggerService);
  });

  describe('_replaceSensitiveValues (via log methods)', () => {
    it('should mask sensitive properties in a flat object', () => {
      const sensitiveData = {
        username: 'testuser',
        password: 'secretpassword',
        email: '<EMAIL>',
        token: 'jwt-token',
        normalField: 'normal-value',
      };

      loggerService.info(sensitiveData);

      expect(mockWinstonLogger.log).toHaveBeenCalledWith(
        LogLevel.info,
        '',
        expect.objectContaining({
          body: expect.objectContaining({
            username: 'testuser',
            password: loggerConfig.maskedValue,
            email: '<EMAIL>',
            token: 'jwt-token',
            normalField: 'normal-value',
          }),
        }),
      );
    });

    it('should mask sensitive properties in nested objects', () => {
      const nestedData = {
        user: {
          username: 'testuser',
          password: 'secretpassword',
          profile: {
            email: '<EMAIL>',
            token: 'jwt-token',
          },
        },
        normalField: 'normal-value',
      };

      loggerService.info(nestedData);

      expect(mockWinstonLogger.log).toHaveBeenCalledWith(
        LogLevel.info,
        '',
        expect.objectContaining({
          body: expect.objectContaining({
            user: expect.objectContaining({
              username: 'testuser',
              password: loggerConfig.maskedValue,
              profile: expect.objectContaining({
                email: '<EMAIL>',
                token: 'jwt-token',
              }),
            }),
            normalField: 'normal-value',
          }),
        }),
      );
    });

    it('should handle arrays with sensitive data', () => {
      const arrayData = [
        { username: 'user1', password: 'pass1' },
        { username: 'user2', token: 'token2' },
        'normal-string',
      ];

      loggerService.info(arrayData);

      expect(mockWinstonLogger.log).toHaveBeenCalledWith(
        LogLevel.info,
        '',
        expect.objectContaining({
          body: [
            { username: 'user1', password: loggerConfig.maskedValue },
            { username: 'user2', token: 'token2' },
            'normal-string',
          ],
        }),
      );
    });

    it('should handle primitive values', () => {
      const primitive = 'just a string';
      loggerService.info(primitive);

      expect(mockWinstonLogger.log).toHaveBeenCalledWith(
        LogLevel.info,
        '',
        expect.objectContaining({
          body: 'just a string',
        }),
      );
    });
  });

  describe('Logger methods', () => {
    it('should log info message', () => {
      const message = 'Test info';
      const attributes = { userId: 1 };

      loggerService.info(message, attributes);

      expect(mockWinstonLogger.log).toHaveBeenCalledWith(
        LogLevel.info,
        '',
        expect.objectContaining({
          severity: 'INFO',
          body: message,
          attributes,
        }),
      );
    });

    it('should log warn message', () => {
      loggerService.warn('warn');
      expect(mockWinstonLogger.log).toHaveBeenCalled();
    });

    it('should log error message', () => {
      loggerService.error('error');
      expect(mockWinstonLogger.log).toHaveBeenCalled();
    });

    it('should log debug message', () => {
      loggerService.debug('debug');
      expect(mockWinstonLogger.log).toHaveBeenCalled();
    });

    it('should log verbose message', () => {
      loggerService.verbose('verbose');
      expect(mockWinstonLogger.log).toHaveBeenCalled();
    });

    it('should log custom log level', () => {
      loggerService.log(LogLevel.silly, 'silly test');
      expect(mockWinstonLogger.log).toHaveBeenCalledWith(
        LogLevel.silly,
        '',
        expect.objectContaining({
          severity: 'SILLY',
          body: 'silly test',
        }),
      );
    });
  });
});
