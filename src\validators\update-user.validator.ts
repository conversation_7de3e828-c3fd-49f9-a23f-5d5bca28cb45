import { z } from 'zod';

export const UpdateUserSchema = z.object({
  username: z
    .string({
      error: (inputField) => (inputField.input === undefined ? 'Username is required' : 'Username must be a string'),
    })
    .min(2, 'Username must be at least 2 characters long'),

  email: z.email({
    error: (inputField) => (inputField.input === undefined ? 'Email is required' : 'Invalid email'),
  }),

  password: z
    .string({
      error: (inputField) => (inputField.input === undefined ? 'Password is required' : 'Password must be a string'),
    })
    .min(6, 'Password must be at least 6 characters long'),
});

export type UpdateUserDTO = z.infer<typeof UpdateUserSchema>;
