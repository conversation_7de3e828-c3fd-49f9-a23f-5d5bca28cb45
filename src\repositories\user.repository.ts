import type { UserFilterInterface } from '@interfaces';
import { User } from '@models';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import bcrypt from 'bcryptjs';
import { CreateUserDTO } from '@validators';
import { DeleteResult, Like, Repository, type FindManyOptions } from 'typeorm';

@Injectable()
export class UserRepository {
  constructor(@InjectRepository(User) private repo: Repository<User>) {}
  /**
   *
   * @param filter UserFilterInterface
   * @returns
   */
  findUsersByFilter = async (filter: UserFilterInterface): Promise<[User[], number]> => {
    const queryOptions: FindManyOptions<User> = {
      skip: (filter.pagination.page - 1) * filter.pagination.limit,
      take: filter.pagination.limit,
      where: {
        id: filter.id,
        username: filter.username ? Like(`%${filter.username}%`) : filter.username,
        email: filter.email ? Like(`%${filter.email}%`) : filter.email,
        createdAt: filter.createdAt,
        updatedAt: filter.updatedAt,
      },
      order: {
        [filter.pagination.sortField]: filter.pagination.sortOrder,
      },
    };

    if (filter.searchKey) {
      queryOptions.where = [
        {
          id: filter.id,
          createdAt: filter.createdAt,
          updatedAt: filter.updatedAt,
        },
        { username: Like(`%${filter.searchKey}%`) },
        { email: Like(`%${filter.searchKey}%`) },
      ];
    }
    return this.repo.findAndCount(queryOptions);
  };

  saveUser = (userDetails: CreateUserDTO): Promise<User> => {
    const password = bcrypt.hashSync(userDetails.password, 8);

    return this.repo.save({ ...userDetails, password });
  };

  deleteUser = (id: number): Promise<DeleteResult> => {
    return this.repo.delete({ id: id });
  };
}
