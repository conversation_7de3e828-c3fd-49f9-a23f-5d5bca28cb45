import type { RequestInterface, ValidationSchemaInterface } from '@interfaces';
import type { NextFunction, Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import type <PERSON><PERSON> from 'joi';

/**
 * Validates request data (body, query, params) against a Joi schema.
 *
 * @param schema ValidationSchemaInterface
 * @returns Express middleware function.
 */
export const validate = (schema: ValidationSchemaInterface) => {
  return (req: RequestInterface, res: Response, next: NextFunction): void => {
    const validationErrors: { [key: string]: Joi.ValidationErrorItem[] } = {};

    // Validate request body
    if (schema.body) {
      const { error } = schema.body.validate(req.body, { abortEarly: false });
      if (error) {
        validationErrors.body = error.details;
      }
    }

    // Validate request query parameters
    if (schema.query) {
      const { error } = schema.query.validate(req.query, { abortEarly: false });
      if (error) {
        validationErrors.query = error.details;
      }
    }

    // Validate request URL parameters
    if (schema.params) {
      const { error } = schema.params.validate(req.params, { abortEarly: false });
      if (error) {
        validationErrors.params = error.details;
      }
    }

    // If there are any validation errors, send a 400 Bad Request response
    if (Object.keys(validationErrors).length > 0) {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'Request validation failed',
        errors: validationErrors,
      });
    } else {
      // If validation passes, proceed to the next middleware/route handler
      next();
    }
  };
};
