# A basic Bitbucket pipeline to build a NodeJS application, run checks, and
# then build and push a Docker image to AWS ECR.

# Use a Node.js image to handle the initial build and test steps.
image: node:22-slim

# Define the variables that you will need. These should be configured in
# your Bitbucket repository settings as secure variables.
# You will need to replace the placeholders with your actual values.
# ECR_REPO_NAME: The name of your ECR repository (e.g., 'my-nodejs-app').
# AWS_DEFAULT_REGION: The AWS region your ECR is in (e.g., 'us-east-1').
# AWS_ACCESS_KEY_ID: Your AWS access key ID.
# AWS_SECRET_ACCESS_KEY: Your AWS secret access key.
options:
  # Set a high memory limit for the Docker build step to avoid timeouts.
  # This is a good practice for memory-intensive tasks.
  size: 2x

pipelines:
  custom:
    bitbucketpipeline:
      - step:
          name: Install dependencies and run checks
          # Use a Node.js image to handle the initial build and test steps.
          caches:
            # Cache the node_modules to speed up subsequent builds.
            - node
          script:
            # Install project dependencies
            - npm install

            # 1. Prettier Check
            # Checks if the code is formatted according to Prettier rules.
            # The --check flag prevents it from reformatting files.
            - npm run prettier:fix

            # 2. Lint Check
            # Runs ESLint to check for code quality and style issues.
            - npm run lint:fix

            # 3. Unit Test
            # Runs all unit tests. The pipeline will fail if any tests fail.
            - npm run test
      - step:
          name: Build and Push Docker Image to ECR
          services:
            # Use Docker service to build and push the image.
            - docker
          script:
            # Explicitly build the Docker image first.
            # The "-t" flag tags the image with the name "nodejs-app".
            - docker build -t $ECR_REPO_NAME:$BITBUCKET_COMMIT --target production .

            # Then, use the Atlassian pipe to authenticate and push the image.
            # The pipe will now find the "nodejs-app" image.
            - pipe: atlassian/aws-ecr-push-image:2.6.0
              variables:
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
                #ECR_REPOSITORY: $ECR_REPO_NAME
                # IMAGE_NAME: The name of the image to push. This must match
                # the tag used in the 'docker build' command above.
                IMAGE_NAME: $ECR_REPO_NAME
                # TAGS: The tag for the Docker image. We use the commit hash
                # to create a unique and traceable tag.
                TAGS: '$BITBUCKET_COMMIT'
      - step:
          name: Deploy to Development ECS Fargate Cluster
          image: atlassian/default-image:3
          script:
            - curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
            - unzip awscliv2.zip
            - ./aws/install

            # Get the URI of the newly built Docker image from your previous step.
            - export ECR_URI="$AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$ECR_REPO_NAME"

            # 1. Register a new task definition
            # First, get the JSON of your existing task definition.
            - aws ecs describe-task-definition --task-definition $DEV_TASK_DEFINATION_NAME > "task-definition.json"

            # Use jq to update the image URI and remove automatically generated fields.
            - NEW_TASK_DEFINITION=$(jq --arg IMAGE "$ECR_URI:$BITBUCKET_COMMIT" '.taskDefinition | del(.taskDefinitionArn) | del(.revision) | del(.status) | del(.compatibilities) | del(.requiresAttributes) | del(.registeredAt) | del(.registeredBy) | .containerDefinitions[0].image = $IMAGE' "task-definition.json")

            # Register the new task definition with ECS.
            - NEW_TASK_DEFINITION_ARN=$(aws ecs register-task-definition --cli-input-json "$NEW_TASK_DEFINITION" | jq -r '.taskDefinition.taskDefinitionArn')

            # 2. Update the ECS service to use the new task definition
            - aws ecs update-service --cluster $DEV_CLUSTER_NAME --service $DEV_SERVICE_NAME --task-definition "$NEW_TASK_DEFINITION_ARN"

      # - step:
      #     name: Deploy to Test ECS Fargate Cluster
      #     deployment: Test
      #     trigger: manual
      #     image: atlassian/default-image:3
      #     script:
      #       - curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
      #       - unzip awscliv2.zip
      #       - ./aws/install

      #       # Get the URI of the newly built Docker image from your previous step.
      #       - export ECR_URI="$AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$ECR_REPO_NAME"

      #       # 1. Register a new task definition
      #       # First, get the JSON of your existing task definition.
      #       - aws ecs describe-task-definition --task-definition $TEST_TASK_DEFINATION_NAME > "test-task-definition.json"

      #       # Use jq to update the image URI and remove automatically generated fields.
      #       - NEW_TASK_DEFINITION=$(jq --arg IMAGE "$ECR_URI:$BITBUCKET_COMMIT" '.taskDefinition | del(.taskDefinitionArn) | del(.revision) | del(.status) | del(.compatibilities) | del(.requiresAttributes) | del(.registeredAt) | del(.registeredBy) | .containerDefinitions[0].image = $IMAGE' "test-task-definition.json")

      #       # Register the new task definition with ECS.
      #       - NEW_TASK_DEFINITION_ARN=$(aws ecs register-task-definition --cli-input-json "$NEW_TASK_DEFINITION" | jq -r '.taskDefinition.taskDefinitionArn')

      #       # 2. Update the ECS service to use the new task definition
      #       - aws ecs update-service --cluster $TEST_CLUSTER_NAME --service $TEST_SERVICE_NAME --task-definition "$NEW_TASK_DEFINITION_ARN"
      # - step:
      #     name: Deploy to Staging ECS Fargate Cluster
      #     deployment: Staging
      #     trigger: manual
      #     image: atlassian/default-image:3
      #     script:
      #       - curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
      #       - unzip awscliv2.zip
      #       - ./aws/install

      #       # Get the URI of the newly built Docker image from your previous step.
      #       - export ECR_URI="$AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$ECR_REPO_NAME"

      #       # 1. Register a new task definition
      #       # First, get the JSON of your existing task definition.
      #       - aws ecs describe-task-definition --task-definition $STAGING_TASK_DEFINATION_NAME > "staging-task-definition.json"

      #       # Use jq to update the image URI and remove automatically generated fields.
      #       - NEW_TASK_DEFINITION=$(jq --arg IMAGE "$ECR_URI:$BITBUCKET_COMMIT" '.taskDefinition | del(.taskDefinitionArn) | del(.revision) | del(.status) | del(.compatibilities) | del(.requiresAttributes) | del(.registeredAt) | del(.registeredBy) | .containerDefinitions[0].image = $IMAGE' "staging-task-definition.json")

      #       # Register the new task definition with ECS.
      #       - NEW_TASK_DEFINITION_ARN=$(aws ecs register-task-definition --cli-input-json "$NEW_TASK_DEFINITION" | jq -r '.taskDefinition.taskDefinitionArn')

      #       # 2. Update the ECS service to use the new task definition
      #       - aws ecs update-service --cluster $STAGING_CLUSTER_NAME --service $STAGING_SERVICE_NAME --task-definition "$NEW_TASK_DEFINITION_ARN"
