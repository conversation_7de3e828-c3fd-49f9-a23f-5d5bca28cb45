# Assigned values are default. In case of no variable or empty values, system will use mentioned defaults. 
# Kindly make sure to verify these as per your enviournment before deployment.

# General
PORT=3000
BASE_URL=""
NODE_ENV="development"
JWT_SECRET=""

# Your application current version maintain, this will be used for observability. It can be in sync with release version.
APP_VERSION="1.0.0"

# Logs Configuration 
# LOGS_READABLE_FORMAT=0 # 0 = unformatted json, 2 = formatted json.
LOG_LEVEL="debug" # error | warn | info | debug | verbose | silly
LOGGER_TIMESTAMP_FORMAT="YYYY-MM-DD HH:mm:ss" # logger timestamp format.
MASK_PROPERTIES="password" # Comma saperated list of properties. Its values will be replaced with ***** in logs.

# API Pagination
DEFAULT_PAGINATION_LIMIT=10
MAX_PAGINATION_LIMIT=100

# Database Configuration
DATABASE_DIALECT="postgres" # mysql | postgres | sqlite | mariadb | mssql
DATABASE_HOST=5432
DATABASE_PORT=
DATABASE_USERNAME=
DATABASE_PASSWORD=
DATABASE_DATABASE=
DATABASE_LOGGING="true"

# Swagger
SWAGGER_ENDPOINT="/documentation"