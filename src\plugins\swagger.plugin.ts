import { commonConfig, swaggerConfig } from '@configs';
import type { SwaggerInterface } from '@interfaces';
import type { Express } from 'express';
import express from 'express';
import fs from 'fs-extra';
import path from 'path';
import swaggerJSDoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';
import yaml from 'yaml';
import { LoggerService } from '../services/logger.service';

// Serve Swagger documentation at /documentation
export const expressSwagger: Express = express();

void (async (): Promise<void> => {
  const sourceDir = './src/swagger/';
  const includeFileTypes = '.swagger.yaml';
  const mainFile = 'main.swagger.yaml';
  const excludedFile = 'merged.swagger.yaml';
  const logger = new LoggerService();

  // Read and parse the main YAML file
  const mainYaml = yaml.parse(await fs.readFile(path.join(sourceDir, mainFile), 'utf8')) as SwaggerInterface;

  const servers: { url: string; description?: string }[] = [
    {
      url: commonConfig.baseUrl,
      description: commonConfig.nodeEnv,
    },
  ];
  mainYaml.servers?.forEach((item) => {
    if (item.url) {
      servers.push({ url: item.url, description: item.description });
    }
  });

  const includedFileNames = fs
    .readdirSync(sourceDir)
    .filter(
      (file: string) =>
        // read all except of main and merged file.
        file.endsWith(includeFileTypes) && !file.startsWith(excludedFile),
    )
    .map((item: string) => path.join(sourceDir, item));

  // Swagger options to specify API definition
  const swaggerOptions: swaggerJSDoc.Options = {
    definition: {
      openapi: mainYaml.openapi,
      info: {
        title: mainYaml.info.title,
        version: mainYaml.info.version,
        description: mainYaml.info.description,
      },
      servers,
    },
    // Path to API route files for JSDoc annotations
    apis: includedFileNames,
  };

  // Generate Swagger documentation
  const swaggerDocs = swaggerJSDoc(swaggerOptions);

  expressSwagger.use(
    swaggerConfig.endpoint, // swagger documentation URL endpoint.
    // Include middleware if needed.
    // (req: Request, res: Response, next: NextFunction) => {
    //   next();
    // },
    swaggerUi.serve,
    swaggerUi.setup(swaggerDocs, {
      explorer: true, // api endpoint search enable.
      customSiteTitle: mainYaml.info.title,
    }),
  );

  logger.info(`[swagger]: Swagger is running at ${commonConfig.baseUrl.slice(0, -1)}${swaggerConfig.endpoint}`);
})();
