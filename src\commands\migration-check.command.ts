import { execSync } from 'child_process';
import { LoggerService } from '../services/logger.service';

export const migrationCheck = (): void => {
  const logger = new LoggerService();
  // ANSI color codes for console formatting
  const reset = '\u001b[0m';
  const bold = '\u001b[1m';
  const red = '\u001b[31m';
  const green = '\u001b[32m';
  const yellow = '\u001b[33m';
  const blue = '\u001b[34m';

  const errorMessage = `
  ${bold}${red}❌ Error: Pending migrations detected.${reset}

  It looks like your database schema is not in sync with your entities.

  ${bold}${green}➡️ Please follow these steps to resolve the issue:${reset}
  1. Run the migration command to apply any pending changes:
    ${yellow}npm run migration:run${reset}

  2. If the issue persists, it means a new migration is needed:
    a. Generate the migration:
        ${yellow}npm run commands -- migration:generate "MigrationName"${reset}
    b. Apply the generated migration:
        ${yellow}npm run migration:run${reset}

  ${bold}${blue}🔁 After completing these steps, rerun the command to verify.${reset}

  ${bold}${yellow}💡 Tip: Always generate a migration after making changes to your entities.${reset}
`;

  try {
    const command = 'npm run typeorm -- schema:log';
    logger.info(`Checking Migration: ${command}`);
    const output = execSync(command, { encoding: 'utf-8' });
    if (!output.includes('Your schema is up to date')) {
      console.log(errorMessage);
      process.exit(1); // Exit with error
    } else {
      console.log(green, 'No pending migrations detected.');
      process.exit(0); // Exit successfully
    }
  } catch (error) {
    logger.error('Error occurred while checking migrations.');
    logger.error(error);
    process.exit(1);
  }
};
