import { dataSourceOptions } from '@configs';
import { DataSource } from 'typeorm';
import { LoggerService } from '../services/logger.service';

export class DatabasePlugin {
  dbInstance: DataSource;
  private readonly _logger = new LoggerService();

  constructor() {
    this.dbInstance = new DataSource(dataSourceOptions);
  }

  async connect(): Promise<DataSource> {
    try {
      if (!this.dbInstance.isInitialized) {
        await this.dbInstance.initialize();
        this._logger.info('Database connection initialized successfully');
      }
      return this.dbInstance;
    } catch (error: unknown) {
      this._logger.error('Failed to initialize database connection.');
      this._logger.error(error);
      throw new Error('Failed to initialize database connection.');
    }
  }

  getConnection(): DataSource {
    if (!this.dbInstance.isInitialized) {
      this._logger.error('Database connection not initialized. Call connect() first.');
      throw new Error('Database connection not initialized. Call connect() first.');
    }
    return this.dbInstance;
  }

  async disconnect(): Promise<void> {
    if (this.dbInstance.isInitialized) {
      await this.dbInstance.destroy();
      this._logger.info('Database connection closed.');
    }
  }
}
