import { Module } from '@nestjs/common';
import { UserRepository } from '@repository';
import { LoggerService } from '@services';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '@models';

@Module({
  imports: [TypeOrmModule.forFeature([User])],
  controllers: [UsersController],
  providers: [LoggerService, UsersService, UserRepository],
  exports: [LoggerService],
})
export class UsersModule {}
