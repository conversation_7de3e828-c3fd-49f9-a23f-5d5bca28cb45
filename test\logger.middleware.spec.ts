import type { RequestInterface } from '../src/interfaces';
import { attachRequestId, requestIdStorage } from '../src/middlewares';
import type { NextFunction, Response } from 'express';
import * as uuid from 'uuid';

describe('logger.middleware - attachRequestId', () => {
  it('should attach a uuid to req.requestId and call next()', () => {
    const req: RequestInterface = {
      requestId: undefined,
      get: jest.fn(),
      header: jest.fn(),
      accepts: jest.fn(),
      acceptsCharsets: jest.fn(),
      acceptsEncodings: jest.fn(),
      acceptsLanguages: jest.fn(),
      is: jest.fn(),
      // Add other required stubs if needed
    } as unknown as RequestInterface;

    const res: Response = undefined as unknown as Response;

    const next: NextFunction = jest.fn();
    const fakeUuid = 'test-uuid';
    jest.spyOn(uuid, 'v4').mockReturnValue(fakeUuid as unknown as Uint8Array);

    const runSpy = jest.spyOn(requestIdStorage, 'run').mockImplementation((_id: string, cb: () => void) => {
      cb();
    });

    attachRequestId(req, res, next);

    expect(req.requestId).toBe(fakeUuid);
    expect(runSpy).toHaveBeenCalledWith(fakeUuid, expect.any(Function));
    expect(next).toHaveBeenCalled();
  });
});
