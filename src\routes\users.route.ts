// TODO: Adjust all routes as per nestjs implementation. Fix the errors related to basic implementation, and then delete this file and whole routes directory - routing will be hadeled by nestjs itself, based on the controllers.

// GET, "/" - refactor into nest implementation, similarly to get by id
// GET, "/:id" - this one is done, as a reference
// POST, "/" - refactor into nest implementation, similarly to get by id
// PUT, "/:id" - refactor into nest implementation, similarly to get by id
// DELETE, "/:id" - implement from scratch

// BELOW lines left for now just as a reference to memorize what we shall adjust.

// usersRouter.get('/', validate({ query: listPaginationQuery.concat(getUsersValidator) }), usersController.fetchUsers);
// usersRouter.post('/', validate({ body: createUserValidator }), usersController.createUser);
// usersRouter.put(
//   '/:id',
//   validate({ params: updateUserValidatorParams, body: updateUserValidatorBody }),
//   usersController.updateUser,
// );
